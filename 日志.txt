index.vue:819 获取模板数据用于复制: {templateId: 722, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
index.vue:826 从模板数据中获取subType: 1
index.vue:948 处理后的复制模板数据: {templateId: undefined, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateEditor.vue:1090 TemplateEditor - 保存复制模板原始数据: Proxy(Object) {scene: '推送', useId: 2, smsExample: '信息', aimSmsSigns: Array(0), factoryInfos: 'huawei'}
TemplateEditor.vue:1097 TemplateEditor - 设置复制模式标识: true
ClickEventSettings.vue:2334 缓存不存在: default_header-image_header-image-click-event
syncSettingToContent @ ClickEventSettings.vue:2334
（匿名） @ ClickEventSettings.vue:2061
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:4901
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
hook.__weh.hook.__weh @ chunk-CAGAHDR2.js?v=0ed09a29:4881
invokeArrayFns @ chunk-CAGAHDR2.js?v=0ed09a29:79
unmountComponent @ chunk-CAGAHDR2.js?v=0ed09a29:8017
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7917
unmountChildren @ chunk-CAGAHDR2.js?v=0ed09a29:8042
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7941
unmountComponent @ chunk-CAGAHDR2.js?v=0ed09a29:8022
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7917
unmountChildren @ chunk-CAGAHDR2.js?v=0ed09a29:8042
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7941
unmountChildren @ chunk-CAGAHDR2.js?v=0ed09a29:8042
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7941
unmountComponent @ chunk-CAGAHDR2.js?v=0ed09a29:8022
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7917
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6733
patchBlockChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7136
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7214
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7240
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
patchBlockChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7136
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7054
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7240
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7067
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7240
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7067
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
runIfDirty @ chunk-CAGAHDR2.js?v=0ed09a29:519
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
flushJobs @ chunk-CAGAHDR2.js?v=0ed09a29:2471
Promise.then
queueFlush @ chunk-CAGAHDR2.js?v=0ed09a29:2385
queueJob @ chunk-CAGAHDR2.js?v=0ed09a29:2380
effect2.scheduler @ chunk-CAGAHDR2.js?v=0ed09a29:7532
trigger @ chunk-CAGAHDR2.js?v=0ed09a29:509
endBatch @ chunk-CAGAHDR2.js?v=0ed09a29:567
notify @ chunk-CAGAHDR2.js?v=0ed09a29:827
trigger @ chunk-CAGAHDR2.js?v=0ed09a29:801
set value @ chunk-CAGAHDR2.js?v=0ed09a29:1673
openCopy @ TemplateEditor.vue:1082
（匿名） @ index.vue:977
setTimeout
copyTemplate @ index.vue:961
await in copyTemplate
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleCopyTemplate @ TemplateList.vue:203
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
copyTemplate @ TemplateCard.vue:477
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleClick @ chunk-5CY6DC63.js?v=0ed09a29:7405
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
invoker @ chunk-CAGAHDR2.js?v=0ed09a29:11202
ClickEventSettings.vue:2074 组件切换或同模板内操作，保留字段数据
ClickEventSettings.vue:2090 保存缓存数据(default_header-image_header-image-click-event): {actionType: 'OPEN_EMAIL', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
ClickEventSettings.vue:2334 缓存不存在: default_coupon-button_coupon-button-click-event
syncSettingToContent @ ClickEventSettings.vue:2334
（匿名） @ ClickEventSettings.vue:2061
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:4901
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
hook.__weh.hook.__weh @ chunk-CAGAHDR2.js?v=0ed09a29:4881
invokeArrayFns @ chunk-CAGAHDR2.js?v=0ed09a29:79
unmountComponent @ chunk-CAGAHDR2.js?v=0ed09a29:8017
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7917
unmountChildren @ chunk-CAGAHDR2.js?v=0ed09a29:8042
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7941
unmountComponent @ chunk-CAGAHDR2.js?v=0ed09a29:8022
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7917
unmountChildren @ chunk-CAGAHDR2.js?v=0ed09a29:8042
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7941
unmountChildren @ chunk-CAGAHDR2.js?v=0ed09a29:8042
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7941
unmountComponent @ chunk-CAGAHDR2.js?v=0ed09a29:8022
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7917
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6733
patchBlockChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7136
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7214
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7240
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
patchBlockChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7136
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7054
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7240
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7067
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7240
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7067
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
runIfDirty @ chunk-CAGAHDR2.js?v=0ed09a29:519
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
flushJobs @ chunk-CAGAHDR2.js?v=0ed09a29:2471
Promise.then
queueFlush @ chunk-CAGAHDR2.js?v=0ed09a29:2385
queueJob @ chunk-CAGAHDR2.js?v=0ed09a29:2380
effect2.scheduler @ chunk-CAGAHDR2.js?v=0ed09a29:7532
trigger @ chunk-CAGAHDR2.js?v=0ed09a29:509
endBatch @ chunk-CAGAHDR2.js?v=0ed09a29:567
notify @ chunk-CAGAHDR2.js?v=0ed09a29:827
trigger @ chunk-CAGAHDR2.js?v=0ed09a29:801
set value @ chunk-CAGAHDR2.js?v=0ed09a29:1673
openCopy @ TemplateEditor.vue:1082
（匿名） @ index.vue:977
setTimeout
copyTemplate @ index.vue:961
await in copyTemplate
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleCopyTemplate @ TemplateList.vue:203
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
copyTemplate @ TemplateCard.vue:477
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleClick @ chunk-5CY6DC63.js?v=0ed09a29:7405
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
invoker @ chunk-CAGAHDR2.js?v=0ed09a29:11202
ClickEventSettings.vue:2074 组件切换或同模板内操作，保留字段数据
ClickEventSettings.vue:2090 保存缓存数据(default_coupon-button_coupon-button-click-event): {actionType: 'COPY_PARAMETER', actionUrl: '123', actionPath: '', packageName: Array(0), floorType: '0', …}
ClickEventSettings.vue:2334 缓存不存在: default_coupon-product-settings-button_coupon-product-settings-product-0
syncSettingToContent @ ClickEventSettings.vue:2334
（匿名） @ ClickEventSettings.vue:2061
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:4901
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
hook.__weh.hook.__weh @ chunk-CAGAHDR2.js?v=0ed09a29:4881
invokeArrayFns @ chunk-CAGAHDR2.js?v=0ed09a29:79
unmountComponent @ chunk-CAGAHDR2.js?v=0ed09a29:8017
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7917
unmountChildren @ chunk-CAGAHDR2.js?v=0ed09a29:8042
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7941
unmountComponent @ chunk-CAGAHDR2.js?v=0ed09a29:8022
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7917
unmountChildren @ chunk-CAGAHDR2.js?v=0ed09a29:8042
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7941
unmountChildren @ chunk-CAGAHDR2.js?v=0ed09a29:8042
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7941
unmountComponent @ chunk-CAGAHDR2.js?v=0ed09a29:8022
unmount @ chunk-CAGAHDR2.js?v=0ed09a29:7917
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6733
patchBlockChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7136
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7214
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7240
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
patchBlockChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7136
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7054
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7240
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7067
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
processFragment @ chunk-CAGAHDR2.js?v=0ed09a29:7240
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6756
patchKeyedChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7681
patchChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7595
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7067
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
runIfDirty @ chunk-CAGAHDR2.js?v=0ed09a29:519
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
flushJobs @ chunk-CAGAHDR2.js?v=0ed09a29:2471
Promise.then
queueFlush @ chunk-CAGAHDR2.js?v=0ed09a29:2385
queueJob @ chunk-CAGAHDR2.js?v=0ed09a29:2380
effect2.scheduler @ chunk-CAGAHDR2.js?v=0ed09a29:7532
trigger @ chunk-CAGAHDR2.js?v=0ed09a29:509
endBatch @ chunk-CAGAHDR2.js?v=0ed09a29:567
notify @ chunk-CAGAHDR2.js?v=0ed09a29:827
trigger @ chunk-CAGAHDR2.js?v=0ed09a29:801
set value @ chunk-CAGAHDR2.js?v=0ed09a29:1673
openCopy @ TemplateEditor.vue:1082
（匿名） @ index.vue:977
setTimeout
copyTemplate @ index.vue:961
await in copyTemplate
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleCopyTemplate @ TemplateList.vue:203
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
copyTemplate @ TemplateCard.vue:477
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleClick @ chunk-5CY6DC63.js?v=0ed09a29:7405
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
invoker @ chunk-CAGAHDR2.js?v=0ed09a29:11202
ClickEventSettings.vue:2074 组件切换或同模板内操作，保留字段数据
ClickEventSettings.vue:2090 保存缓存数据(default_coupon-product-settings-button_coupon-product-settings-product-0): {actionType: 'OPEN_SCHEDULE', actionUrl: '', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateFactory.js:67 传入的template: {templateId: 1, userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: {templateId: 2, userId: null, appKey: null, cardId: 'com.hbm.redpacket', templateName: '红包', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: redpacket
TemplateFactory.js:67 传入的template: {templateId: 3, userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multitext
TemplateFactory.js:67 传入的template: {templateId: 4, userId: null, appKey: null, cardId: 'com.hbm.carouse', templateName: '横滑', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: horizontalswipe
TemplateFactory.js:67 传入的template: {templateId: 5, userId: null, appKey: null, cardId: 'com.hbm.videoimageandtext', templateName: '视频图文', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: videoimageandtext
TemplateFactory.js:67 传入的template: {templateId: 6, userId: null, appKey: null, cardId: 'com.hbm.videoimageandtext2', templateName: '图文视频', …}
 cardId匹配成功，返回类型: videoimageandtext
 传入的template: {templateId: 7, userId: null, appKey: null, cardId: 'com.hbm.pureText', templateName: '长文本', …}
 cardId匹配成功，返回类型: longtext
 传入的template: {templateId: 8, userId: null, appKey: null, cardId: 'com.hbm.video', templateName: '视频', …}
 cardId匹配成功，返回类型: video
 传入的template: {templateId: 9, userId: null, appKey: null, cardId: 'com.hbm.carouselImageSixteenToNine', templateName: '图片轮播16:9', …}
 cardId匹配成功，返回类型: carousel
 传入的template: {templateId: 10, userId: null, appKey: null, cardId: 'com.hbm.carouselQuareImage', templateName: '图片轮播1:1', …}
 cardId匹配成功，返回类型: carousel
 传入的template: {templateId: 11, userId: null, appKey: null, cardId: 'com.hbm.carouselVerticalImage', templateName: '图片轮播48:65', …}
 cardId匹配成功，返回类型: carousel
 传入的template: {templateId: 12, userId: null, appKey: null, cardId: 'com.hbm.ecImageAndText', templateName: '电商', …}
 cardId匹配成功，返回类型: ecommerce
 传入的template: {templateId: 13, userId: null, appKey: null, cardId: 'com.hbm.ecommerce', templateName: '电商(多商品)', …}
 cardId匹配成功，返回类型: multiproduct
 传入的template: {templateId: 15, userId: null, appKey: null, cardId: 'com.hbm.cardVoucher', templateName: '单卡券', …}
 cardId匹配成功，返回类型: cardvoucher
 传入的template: {templateId: 16, userId: null, appKey: null, cardId: 'com.hbm.notification', templateName: '一般通知类', …}
 cardId匹配成功，返回类型: notification
 传入的template: {templateId: 17, userId: null, appKey: null, cardId: 'com.hbm.notification', templateName: '增强通知类', …}
 cardId匹配成功，返回类型: notification
 传入的template: {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', …}
 cardId匹配成功，返回类型: couponproduct
 TemplateEditor - 复制模式：查找匹配模板 {originalTemplateName: '测试电商模板', originalCardId: 'com.hbm.ecommerceCouponVertical.v2', templateDataTplType: null, availableTemplates: Array(17)}
 TemplateEditor - 复制模式：找到匹配模板 Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', …}
 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
 cardId匹配成功，返回类型: couponproduct
 CouponProductTemplate - 初始化模板内容: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
 CouponProductTemplate - 原始contents: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 CouponProductTemplate - 初始化后的contents: (22) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
 cardId匹配成功，返回类型: couponproduct
 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
 cardId匹配成功，返回类型: couponproduct
 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
 cardId匹配成功，返回类型: couponproduct
 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
 cardId匹配成功，返回类型: couponproduct
 TemplateEditor - 计算券+商品显示数据
 TemplateEditor - 提取券+商品数据，内容: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object), 8: Proxy(Object), 9: Proxy(Object), 10: Proxy(Object), 11: Proxy(Object), 12: Proxy(Object), 13: Proxy(Object), 14: Proxy(Object), 15: Proxy(Object), 16: Proxy(Object), 17: Proxy(Object), 18: Proxy(Object), 19: Proxy(Object), 20: Proxy(Object), 21: Proxy(Object)}
 CouponProductTemplate - 处理券+商品内容数据，输入contents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object), 8: Proxy(Object), 9: Proxy(Object), 10: Proxy(Object), 11: Proxy(Object), 12: Proxy(Object), 13: Proxy(Object), 14: Proxy(Object), 15: Proxy(Object), 16: Proxy(Object), 17: Proxy(Object), 18: Proxy(Object), 19: Proxy(Object), 20: Proxy(Object), 21: Proxy(Object)}
 CouponProductTemplate - 映射 positionNumber 1: {index: 0, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100926233510024.jpeg'}
 CouponProductTemplate - 映射 positionNumber 2: {index: 1, type: 'text', content: '编辑文本，最多12个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 3: {index: 2, type: 'text', content: '编辑文本，最多17个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 4: {index: 3, type: 'text', content: '99', src: null}
 CouponProductTemplate - 映射 positionNumber 5: {index: 4, type: 'text', content: '编辑文本，最多11个字', src: null}
 CouponProductTemplate - 映射 positionNumber 6: {index: 5, type: 'text', content: '编辑文本，最多32个中文字;编辑文本，最多32个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 7: {index: 6, type: 'button', content: '领', src: null}
 CouponProductTemplate - 映射 positionNumber 8: {index: 7, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100926233510024.jpeg'}
 CouponProductTemplate - 映射 positionNumber 9: {index: 8, type: 'text', content: '编辑文本，最多28个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 10: {index: 9, type: 'text', content: '编辑文本', src: null}
 CouponProductTemplate - 映射 positionNumber 11: {index: 10, type: 'text', content: '100.00', src: null}
 CouponProductTemplate - 映射 positionNumber 12: {index: 11, type: 'button', content: '立即购买', src: null}
 CouponProductTemplate - 映射 positionNumber 13: {index: 12, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100925014010023.jpeg'}
 CouponProductTemplate - 映射 positionNumber 14: {index: 13, type: 'text', content: '编辑文本，最多28个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 15: {index: 14, type: 'text', content: '编辑文本', src: null}
 CouponProductTemplate - 映射 positionNumber 16: {index: 15, type: 'text', content: '100.00', src: null}
 CouponProductTemplate - 映射 positionNumber 17: {index: 16, type: 'button', content: '立即购买', src: null}
 CouponProductTemplate - 映射 positionNumber 18: {index: 17, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100923797710022.png'}
 CouponProductTemplate - 映射 positionNumber 19: {index: 18, type: 'text', content: '编辑文本，最多28个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 20: {index: 19, type: 'text', content: '编辑文本', src: null}
 CouponProductTemplate - 映射 positionNumber 21: {index: 20, type: 'text', content: '100.00', src: null}
 CouponProductTemplate - 映射 positionNumber 22: {index: 21, type: 'button', content: '立即购买', src: null}
 CouponProductTemplate - contentMap 键值: (22) [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]
 处理券金额数据: Proxy(Object) {contentId: 5884, pageId: 772, templateId: 722, type: 'text', content: '99', …}
 CouponProductTemplate - 开始处理商品数据，配置: (3) [{…}, {…}, {…}]
 CouponProductTemplate - 处理商品 1，起始位置: 8
 CouponProductTemplate - 商品 1 图片 (位置8): /aim_files/A173440270073611/M175100926233510024.jpeg
 CouponProductTemplate - 商品 1 标题 (位置9): 编辑文本，最多28个中文字
 CouponProductTemplate - 商品 1 标签 (位置10): 编辑文本
 CouponProductTemplate - 商品 1 价格 (位置11): 100.00
 CouponProductTemplate - 商品 1 按钮 (位置12): 立即购买
 CouponProductTemplate - 商品 1 最终数据: {image: '/aim_files/A173440270073611/M175100926233510024.jpeg', title: '编辑文本，最多28个中文字', tag: '编辑文本', price: '100.00', buttonText: '立即购买'}
 CouponProductTemplate - 商品 1 已添加到结果中
 CouponProductTemplate - 处理商品 2，起始位置: 13
 CouponProductTemplate - 商品 2 图片 (位置13): /aim_files/A173440270073611/M175100925014010023.jpeg
 CouponProductTemplate - 商品 2 标题 (位置14): 编辑文本，最多28个中文字
 CouponProductTemplate - 商品 2 标签 (位置15): 编辑文本
 CouponProductTemplate - 商品 2 价格 (位置16): 100.00
 CouponProductTemplate - 商品 2 按钮 (位置17): 立即购买
 CouponProductTemplate - 商品 2 最终数据: {image: '/aim_files/A173440270073611/M175100925014010023.jpeg', title: '编辑文本，最多28个中文字', tag: '编辑文本', price: '100.00', buttonText: '立即购买'}
 CouponProductTemplate - 商品 2 已添加到结果中
 CouponProductTemplate - 处理商品 3，起始位置: 18
 CouponProductTemplate - 商品 3 图片 (位置18): /aim_files/A173440270073611/M175100923797710022.png
 CouponProductTemplate - 商品 3 标题 (位置19): 编辑文本，最多28个中文字
 CouponProductTemplate - 商品 3 标签 (位置20): 编辑文本
 CouponProductTemplate - 商品 3 价格 (位置21): 100.00
 CouponProductTemplate - 商品 3 按钮 (位置22): 立即购买
 CouponProductTemplate - 商品 3 最终数据: {image: '/aim_files/A173440270073611/M175100923797710022.png', title: '编辑文本，最多28个中文字', tag: '编辑文本', price: '100.00', buttonText: '立即购买'}
 CouponProductTemplate - 商品 3 已添加到结果中
 CouponProductTemplate - 券+商品数据处理完成: {headerProduct: {…}, coupon: {…}, products: Array(3)}
 TemplateEditor - 使用CouponProductTemplate处理券+商品数据: {headerProduct: {…}, coupon: {…}, products: Array(3)}
 TemplateEditor - 复制模式，提取详细点击事件字段
 TemplateEditor - 找到券按钮内容: Proxy(Object) {contentId: 5887, pageId: 772, templateId: 722, type: 'button', content: '领', …}
 TemplateEditor - 复制模式券按钮点击事件已增强: {actionType: 'COPY_PARAMETER', actionUrl: '123', actionPath: '', packageName: undefined, floorType: '0', …}
 TemplateEditor - 复制模式头部图片点击事件已增强: {actionType: 'OPEN_EMAIL', actionUrl: '', actionPath: '', packageName: undefined, floorType: '0', …}
 TemplateEditor - 复制模式商品1按钮点击事件已增强: {actionType: 'OPEN_SCHEDULE', actionUrl: '', actionPath: '', packageName: undefined, floorType: '0', …}
 TemplateEditor - 复制模式商品2按钮点击事件已增强: {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: undefined, floorType: '0', …}
 TemplateEditor - 复制模式商品3按钮点击事件已增强: {actionType: 'OPEN_SMS', actionUrl: '', actionPath: '', packageName: undefined, floorType: '0', …}
 TemplateEditor - 从模板内容提取券+商品显示数据: {headerProduct: {…}, coupon: {…}, products: Array(3)}
 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
 cardId匹配成功，返回类型: couponproduct
 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
 cardId匹配成功，返回类型: couponproduct
 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
 cardId匹配成功，返回类型: couponproduct
 === rendererComponent 调试信息 ===
 props.templateType: couponproduct
 选择的渲染器组件: {__name: 'CouponProductTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '43bab64b', setup: ƒ, …}
 CouponProductTemplateRenderer - 计算显示数据
 CouponProductTemplateRenderer - 使用父组件传递的数据: {headerProduct: {…}, coupon: {…}, products: Array(3)}
 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
 TemplateEditor - 复制模式：解析模板内容
 TemplateEditor - 复制模式：原始pages数据: [{…}]
 TemplateEditor - 复制模式：已设置模板内容: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 TemplateEditor - 复制模式：检查positionNumber保留情况:
 内容 0: positionNumber=1, type=image, content=null
 内容 1: positionNumber=2, type=text, content=编辑文本，最多12个中文字
 内容 2: positionNumber=3, type=text, content=编辑文本，最多17个中文字
 内容 3: positionNumber=4, type=text, content=99
 内容 4: positionNumber=5, type=text, content=编辑文本，最多11个字
 内容 5: positionNumber=6, type=text, content=编辑文本，最多32个中文字;编辑文本，最多32个中文字
 内容 6: positionNumber=7, type=button, content=领
 内容 7: positionNumber=8, type=image, content=null
 内容 8: positionNumber=9, type=text, content=编辑文本，最多28个中文字
 内容 9: positionNumber=10, type=text, content=编辑文本
 内容 10: positionNumber=11, type=text, content=100.00
 内容 11: positionNumber=12, type=button, content=立即购买
 内容 12: positionNumber=13, type=image, content=null
 内容 13: positionNumber=14, type=text, content=编辑文本，最多28个中文字
 内容 14: positionNumber=15, type=text, content=编辑文本
 内容 15: positionNumber=16, type=text, content=100.00
 内容 16: positionNumber=17, type=button, content=立即购买
 内容 17: positionNumber=18, type=image, content=null
 内容 18: positionNumber=19, type=text, content=编辑文本，最多28个中文字
 内容 19: positionNumber=20, type=text, content=编辑文本
 内容 20: positionNumber=21, type=text, content=100.00
 内容 21: positionNumber=22, type=button, content=立即购买
 TemplateEditor - 复制模式：最终内容检查: {contentId: 5881, type: 'image', actionType: 'OPEN_EMAIL', emailAddress: undefined, emailSubject: undefined, …}
 TemplateEditor - 复制模式：最终内容检查: {contentId: 5887, type: 'button', actionType: 'COPY_PARAMETER', emailAddress: undefined, emailSubject: undefined, …}
 TemplateEditor - 复制模式：最终内容检查: {contentId: 5888, type: 'image', actionType: null, emailAddress: undefined, emailSubject: undefined, …}
 TemplateEditor - 复制模式：最终内容检查: {contentId: 5892, type: 'button', actionType: 'OPEN_SCHEDULE', emailAddress: undefined, emailSubject: undefined, …}
 TemplateEditor - 复制模式：最终内容检查: {contentId: 5893, type: 'image', actionType: null, emailAddress: undefined, emailSubject: undefined, …}
 TemplateEditor - 复制模式：最终内容检查: {contentId: 5897, type: 'button', actionType: 'OPEN_POPUP', emailAddress: undefined, emailSubject: undefined, …}
 TemplateEditor - 复制模式：最终内容检查: {contentId: 5898, type: 'image', actionType: null, emailAddress: undefined, emailSubject: undefined, …}
 TemplateEditor - 复制模式：最终内容检查: {contentId: 5902, type: 'button', actionType: 'OPEN_SMS', emailAddress: undefined, emailSubject: undefined, …}
 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
 cardId匹配成功，返回类型: couponproduct
 TemplateEditor - 复制模式：检测到券商品模板，初始化设置状态
 TemplateEditor - 复制模式：当前selectedTemplateContents: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 TemplateEditor - 提取券+商品数据，内容: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 CouponProductTemplate - 处理券+商品内容数据，输入contents: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
 CouponProductTemplate - 映射 positionNumber 1: {index: 0, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100926233510024.jpeg'}
 CouponProductTemplate - 映射 positionNumber 2: {index: 1, type: 'text', content: '编辑文本，最多12个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 3: {index: 2, type: 'text', content: '编辑文本，最多17个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 4: {index: 3, type: 'text', content: '99', src: null}
 CouponProductTemplate - 映射 positionNumber 5: {index: 4, type: 'text', content: '编辑文本，最多11个字', src: null}
 CouponProductTemplate - 映射 positionNumber 6: {index: 5, type: 'text', content: '编辑文本，最多32个中文字;编辑文本，最多32个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 7: {index: 6, type: 'button', content: '领', src: null}
 CouponProductTemplate - 映射 positionNumber 8: {index: 7, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100926233510024.jpeg'}
 CouponProductTemplate - 映射 positionNumber 9: {index: 8, type: 'text', content: '编辑文本，最多28个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 10: {index: 9, type: 'text', content: '编辑文本', src: null}
 CouponProductTemplate - 映射 positionNumber 11: {index: 10, type: 'text', content: '100.00', src: null}
 CouponProductTemplate - 映射 positionNumber 12: {index: 11, type: 'button', content: '立即购买', src: null}
 CouponProductTemplate - 映射 positionNumber 13: {index: 12, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100925014010023.jpeg'}
 CouponProductTemplate - 映射 positionNumber 14: {index: 13, type: 'text', content: '编辑文本，最多28个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 15: {index: 14, type: 'text', content: '编辑文本', src: null}
 CouponProductTemplate - 映射 positionNumber 16: {index: 15, type: 'text', content: '100.00', src: null}
 CouponProductTemplate - 映射 positionNumber 17: {index: 16, type: 'button', content: '立即购买', src: null}
 CouponProductTemplate - 映射 positionNumber 18: {index: 17, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100923797710022.png'}
 CouponProductTemplate - 映射 positionNumber 19: {index: 18, type: 'text', content: '编辑文本，最多28个中文字', src: null}
 CouponProductTemplate - 映射 positionNumber 20: {index: 19, type: 'text', content: '编辑文本', src: null}
 CouponProductTemplate - 映射 positionNumber 21: {index: 20, type: 'text', content: '100.00', src: null}
 CouponProductTemplate - 映射 positionNumber 22: {index: 21, type: 'button', content: '立即购买', src: null}
 CouponProductTemplate - contentMap 键值: (22) [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]
 处理券金额数据: Proxy(Object) {contentId: 5884, pageId: 772, templateId: 722, type: 'text', content: '99', …}
 CouponProductTemplate - 开始处理商品数据，配置: (3) [{…}, {…}, {…}]
 CouponProductTemplate - 处理商品 1，起始位置: 8
 CouponProductTemplate - 商品 1 图片 (位置8): /aim_files/A173440270073611/M175100926233510024.jpeg
 CouponProductTemplate - 商品 1 标题 (位置9): 编辑文本，最多28个中文字
 CouponProductTemplate - 商品 1 标签 (位置10): 编辑文本
 CouponProductTemplate - 商品 1 价格 (位置11): 100.00
 CouponProductTemplate - 商品 1 按钮 (位置12): 立即购买
 CouponProductTemplate - 商品 1 最终数据: {image: '/aim_files/A173440270073611/M175100926233510024.jpeg', title: '编辑文本，最多28个中文字', tag: '编辑文本', price: '100.00', buttonText: '立即购买'}
 CouponProductTemplate - 商品 1 已添加到结果中
 CouponProductTemplate - 处理商品 2，起始位置: 13
 CouponProductTemplate - 商品 2 图片 (位置13): /aim_files/A173440270073611/M175100925014010023.jpeg
 CouponProductTemplate - 商品 2 标题 (位置14): 编辑文本，最多28个中文字
 CouponProductTemplate - 商品 2 标签 (位置15): 编辑文本
 CouponProductTemplate - 商品 2 价格 (位置16): 100.00
 CouponProductTemplate - 商品 2 按钮 (位置17): 立即购买
 CouponProductTemplate - 商品 2 最终数据: {image: '/aim_files/A173440270073611/M175100925014010023.jpeg', title: '编辑文本，最多28个中文字', tag: '编辑文本', price: '100.00', buttonText: '立即购买'}
 CouponProductTemplate - 商品 2 已添加到结果中
 CouponProductTemplate - 处理商品 3，起始位置: 18
 CouponProductTemplate - 商品 3 图片 (位置18): /aim_files/A173440270073611/M175100923797710022.png
 CouponProductTemplate - 商品 3 标题 (位置19): 编辑文本，最多28个中文字
 CouponProductTemplate - 商品 3 标签 (位置20): 编辑文本
 CouponProductTemplate - 商品 3 价格 (位置21): 100.00
 CouponProductTemplate - 商品 3 按钮 (位置22): 立即购买
 CouponProductTemplate - 商品 3 最终数据: {image: '/aim_files/A173440270073611/M175100923797710022.png', title: '编辑文本，最多28个中文字', tag: '编辑文本', price: '100.00', buttonText: '立即购买'}
 CouponProductTemplate - 商品 3 已添加到结果中
 CouponProductTemplate - 券+商品数据处理完成: {headerProduct: {…}, coupon: {…}, products: Array(3)}
 TemplateEditor - 使用CouponProductTemplate处理券+商品数据: {headerProduct: {…}, coupon: {…}, products: Array(3)}
 TemplateEditor - 复制模式，提取详细点击事件字段
 TemplateEditor - 找到券按钮内容: Proxy(Object) {contentId: 5887, pageId: 772, templateId: 722, type: 'button', content: '领', …}
 TemplateEditor - 复制模式券按钮点击事件已增强: {actionType: 'COPY_PARAMETER', actionUrl: '123', actionPath: '', packageName: undefined, floorType: '0', …}
 TemplateEditor - 复制模式头部图片点击事件已增强: {actionType: 'OPEN_EMAIL', actionUrl: '', actionPath: '', packageName: undefined, floorType: '0', …}
 TemplateEditor - 复制模式商品1按钮点击事件已增强: {actionType: 'OPEN_SCHEDULE', actionUrl: '', actionPath: '', packageName: undefined, floorType: '0', …}
 TemplateEditor - 复制模式商品2按钮点击事件已增强: {actionType: 'OPEN_POPUP', actionUrl: '', actionPath: '', packageName: undefined, floorType: '0', …}
 TemplateEditor - 复制模式商品3按钮点击事件已增强: {actionType: 'OPEN_SMS', actionUrl: '', actionPath: '', packageName: undefined, floorType: '0', …}
 TemplateEditor - 复制模式：提取的券商品数据: {headerProduct: {…}, coupon: {…}, products: Array(3)}
 TemplateEditor - 复制模式：券商品设置状态已初始化
 TemplateEditor - 复制模式：处理点击事件数据
 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5881, type: 'image', actionType: 'OPEN_EMAIL', actionJson: '{"subject":"2","body":"3","target":"<EMAIL>"}', actionJsonType: 'string'}
 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {subject: '2', body: '3', target: '<EMAIL>'}
 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5881, type: 'image', finalActionType: 'OPEN_EMAIL', hasActionJson: true}
 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5881, type: 'image', actionType: 'OPEN_EMAIL', actionJson: Proxy(Object), extractedFields: {…}}
 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5881,
  "type": "image",
  "actionType": "OPEN_EMAIL",
  "actionUrl": "<EMAIL>",
  "actionPath": "2",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "<EMAIL>",
  "emailSubject": "2",
  "emailBody": "3",
  "scheduleTitle": "<EMAIL>",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "<EMAIL>",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "<EMAIL>"
}
 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5887, type: 'button', actionType: 'COPY_PARAMETER', actionJson: '{"target":"123"}', actionJsonType: 'string'}
 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {target: '123'}
 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5887, type: 'button', finalActionType: 'COPY_PARAMETER', hasActionJson: true}
 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5887, type: 'button', actionType: 'COPY_PARAMETER', actionJson: Proxy(Object), extractedFields: {…}}
 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5887,
  "type": "button",
  "actionType": "COPY_PARAMETER",
  "actionUrl": "123",
  "actionPath": "",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "123",
  "emailSubject": "",
  "emailBody": "",
  "scheduleTitle": "123",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "123",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "123"
}
 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5888, type: 'image', actionType: null, actionJson: null, actionJsonType: 'object'}
 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5888, type: 'image', finalActionType: null, hasActionJson: false}
 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5892, type: 'button', actionType: 'OPEN_SCHEDULE', actionJson: '{"description":"2","beginTime":"2025-07-01 00:00:00","endTime":"2025-07-31 00:00:00","target":"1"}', actionJsonType: 'string'}
 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {description: '2', beginTime: '2025-07-01 00:00:00', endTime: '2025-07-31 00:00:00', target: '1'}
 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5892, type: 'button', finalActionType: 'OPEN_SCHEDULE', hasActionJson: true}
TemplateEditor.vue:1316 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5892, type: 'button', actionType: 'OPEN_SCHEDULE', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1325 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5892,
  "type": "button",
  "actionType": "OPEN_SCHEDULE",
  "actionUrl": "1",
  "actionPath": "2",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "1",
  "emailSubject": "",
  "emailBody": "",
  "scheduleTitle": "1",
  "scheduleContent": "2",
  "scheduleStartTimeString": "2025-07-01 00:00:00",
  "scheduleEndTimeString": "2025-07-31 00:00:00",
  "popupTitle": "1",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "1"
}
TemplateEditor.vue:1262 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5893, type: 'image', actionType: null, actionJson: null, actionJsonType: 'object'}
TemplateEditor.vue:1292 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5893, type: 'image', finalActionType: null, hasActionJson: false}
TemplateEditor.vue:1262 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5897, type: 'button', actionType: 'OPEN_POPUP', actionJson: '{"mode":0,"textButton":"3","content":"2","target":"1"}', actionJsonType: 'string'}
TemplateEditor.vue:1274 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {mode: 0, textButton: '3', content: '2', target: '1'}
TemplateEditor.vue:1292 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5897, type: 'button', finalActionType: 'OPEN_POPUP', hasActionJson: true}
TemplateEditor.vue:1316 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5897, type: 'button', actionType: 'OPEN_POPUP', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1325 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5897,
  "type": "button",
  "actionType": "OPEN_POPUP",
  "actionUrl": "1",
  "actionPath": "2",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "1",
  "emailSubject": "",
  "emailBody": "",
  "scheduleTitle": "1",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "1",
  "popupContent": "2",
  "popupButtonText": "3",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "1"
}
TemplateEditor.vue:1262 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5898, type: 'image', actionType: null, actionJson: null, actionJsonType: 'object'}
TemplateEditor.vue:1292 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5898, type: 'image', finalActionType: null, hasActionJson: false}
TemplateEditor.vue:1262 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5902, type: 'button', actionType: 'OPEN_SMS', actionJson: '{"body":"123","target":"13600001111"}', actionJsonType: 'string'}
TemplateEditor.vue:1274 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {body: '123', target: '13600001111'}
TemplateEditor.vue:1292 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5902, type: 'button', finalActionType: 'OPEN_SMS', hasActionJson: true}
clickEventManager.js:611 SMS extractActionFields: {actionJson: {…}, extractedActionUrl: '13600001111', extractedActionPath: '123', bodyValue: '123'}
TemplateEditor.vue:1316 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5902, type: 'button', actionType: 'OPEN_SMS', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1325 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5902,
  "type": "button",
  "actionType": "OPEN_SMS",
  "actionUrl": "13600001111",
  "actionPath": "123",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "13600001111",
  "emailSubject": "",
  "emailBody": "123",
  "scheduleTitle": "13600001111",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "13600001111",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "13600001111"
}
TemplateEditor.vue:1362 TemplateEditor - 复制模式设置完成，最终确认标识: true
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6664 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6668 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3)}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3)}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
CouponProductTemplateRenderer.vue:127 券+商品模板点击事件
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 'coupon-product-settings', type: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
CouponProductSettings.vue:548 CouponProductSettings - Props内容变化: Proxy(Object) {contentId: 'coupon-product-settings', type: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
CouponProductSettings.vue:563 需要初始化设置
CouponProductSettings.vue:428 CouponProductSettings - 初始化设置，传入数据: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3)}
CouponProductSettings.vue:533 CouponProductSettings - 初始化完成，当前设置: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(3), selectedProductIndex: 0}
ImageElement.vue:106 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', 判断结果: false}
ImageElement.vue:193 ImageElement - 不是轮播图模板，显示普通图片
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'header-image-click-event', type: 'header-image', actionType: 'OPEN_EMAIL', result: false}
ClickEventSettings.vue:727 ClickEventSettings - currentActionType计算: {isHorizontalSwipe: false, contentActionType: 'OPEN_EMAIL', contentType: 'header-image', contentId: 'header-image-click-event'}
ClickEventSettings.vue:783 ClickEventSettings - 非横滑模板actionType: {contentActionType: 'OPEN_EMAIL', result: 'OPEN_EMAIL'}
ClickEventSettings.vue:2472 packageName changed: 
ClickEventSettings.vue:2489 emailAddressModel get (普通模板): 
ClickEventSettings.vue:714 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'coupon-button-click-event', type: 'coupon-button', actionType: 'COPY_PARAMETER', result: false}
 ClickEventSettings - currentActionType计算: {isHorizontalSwipe: false, contentActionType: 'COPY_PARAMETER', contentType: 'coupon-button', contentId: 'coupon-button-click-event'}
 ClickEventSettings - 非横滑模板actionType: {contentActionType: 'COPY_PARAMETER', result: 'COPY_PARAMETER'}
 packageName changed: 
 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', 判断结果: false}
 ImageElement - 不是轮播图模板，显示普通图片
 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: {…}, contentId: 'coupon-product-settings-product-0', type: 'coupon-product-settings-button', actionType: 'OPEN_SCHEDULE', result: false}
 ClickEventSettings - currentActionType计算: {isHorizontalSwipe: false, contentActionType: 'OPEN_SCHEDULE', contentType: 'coupon-product-settings-button', contentId: 'coupon-product-settings-product-0'}
 ClickEventSettings - 非横滑模板actionType: {contentActionType: 'OPEN_SCHEDULE', result: 'OPEN_SCHEDULE'}
 packageName changed: 
 初始化时设置当前选择的AppKey为: A173440270073611
 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
 CouponProductSettings - 组件挂载，开始初始化
 CouponProductSettings - props.content: Proxy(Object) {contentId: 'coupon-product-settings', type: 'coupon-product-settings', isCouponProductSettings: true, currentData: Proxy(Object)}
 ClickEventSettings - 初始化字段详细信息: {contentId: 'header-image-click-event', actionType: 'OPEN_EMAIL', propsEmailAddress: '', propsEmailSubject: '', propsEmailBody: '', …}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_EMAIL', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_EMAIL', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 ClickEventSettings - 初始化字段详细信息: {contentId: 'coupon-button-click-event', actionType: 'COPY_PARAMETER', propsEmailAddress: '', propsEmailSubject: '', propsEmailBody: '', …}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: '123'}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: '123'}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 ClickEventSettings - 初始化字段详细信息: {contentId: 'coupon-product-settings-product-0', actionType: 'OPEN_SCHEDULE', propsEmailAddress: '', propsEmailSubject: '', propsEmailBody: '', …}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_SCHEDULE', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_SCHEDULE', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 没有用户输入，执行初始化字段
 ClickEventSettings - 初始化字段详细信息: {contentId: 'header-image-click-event', actionType: 'OPEN_EMAIL', propsEmailAddress: '', propsEmailSubject: '', propsEmailBody: '', …}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_EMAIL', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_EMAIL', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 没有用户输入，执行初始化字段
 ClickEventSettings - 初始化字段详细信息: {contentId: 'coupon-button-click-event', actionType: 'COPY_PARAMETER', propsEmailAddress: '', propsEmailSubject: '', propsEmailBody: '', …}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: '123'}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'COPY_PARAMETER', actionUrl: '123'}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
 没有用户输入，执行初始化字段
 ClickEventSettings - 初始化字段详细信息: {contentId: 'coupon-product-settings-product-0', actionType: 'OPEN_SCHEDULE', propsEmailAddress: '', propsEmailSubject: '', propsEmailBody: '', …}
 复制类型字段初始化值: {currentCopyType: '1', propsCopyType: '1', cacheCopyType: '1', finalCopyType: '1', currentFixedContent: '', …}
 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
 手动更新触发: {actionType: 'OPEN_SCHEDULE', actionUrl: ''}
 其他更新进行中，跳过重复请求
 手动更新触发: {actionType: 'OPEN_SCHEDULE', actionUrl: ''}
 其他更新进行中，跳过重复请求
 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
 设置为复制参数模式: {copyType: '1', selectedParamId: '', fixedContent: ''}
