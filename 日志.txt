client:495 [vite] connecting...
client:618 [vite] connected.
MediaCategory.vue:239 获取分类列表，filterByAppKey: false filterAppKey: 
MediaCategory.vue:333 更新appKey信息，但不自动选择分类或请求数据, appKey: A173440270073611
index.vue:154 更新appKey和dirId: {newAppKey: 'A173440270073611', newDirId: '', newSubType: undefined}
index.vue:202 收到0个模板数据，总数：0，是否有子分类：true，来源：MediaCategory
index.vue:207 已重置页码到第一页
cacheCleanup.js:224 🧹 缓存清理工具
cacheCleanup.js:225 📊 当前缓存统计:
cacheCleanup.js:226 （索引）totaldevToolsotherexpiredvalid（索引）totaldevToolsotherexpiredvalidlocalStorage110sessionStorage000clickEventCache000Object
cacheCleanup.js:228 🔧 可用的清理命令:
cacheCleanup.js:229 clearVueDevToolsCache() - 清理 Vue DevTools 缓存
cacheCleanup.js:230 clearExpiredSessionStorage() - 清理过期的 sessionStorage
cacheCleanup.js:231 cleanupClickEventCache() - 清理点击事件缓存
cacheCleanup.js:232 performFullCacheCleanup() - 执行完整清理
cacheCleanup.js:245 💡 提示: 使用 window.cacheCleanup.performFullCacheCleanup() 执行完整清理
MediaCategory.vue:352 处理父级分类勾选变化，appName: xjk_test checked: true
MediaCategory.vue:372 切换到新的appName(父分类)，重置状态
MediaCategory.vue:654 发送分类变化事件，选中的分类数量: 5
index.vue:137 分类选择变化，当前选中: 5 个分类
index.vue:142 分类变化: 重置页码到第1页
index.vue:154 更新appKey和dirId: {newAppKey: 'A173440270073611', newDirId: '1005,496,481,466,1033', newSubType: ''}
MediaCategory.vue:427 判断是否为基础版式: false 选中的dirIds: (5) [1005, 496, 481, 466, 1033]
TemplateList.vue:114 TemplateList: 接收到新的模板数据: 0
TemplateList.vue:154 TemplateList: 父组件页码更新为: 1
index.vue:202 收到0个模板数据，总数：0，是否有子分类：true，来源：MediaCategory
index.vue:207 已重置页码到第一页
TemplateList.vue:114 TemplateList: 接收到新的模板数据: 0
MediaCategory.vue:441 获取模板列表成功: {code: 0, msg: '成功', data: {…}}
index.vue:202 收到10个模板数据，总数：372，是否有子分类：true，来源：MediaCategory
index.vue:207 已重置页码到第一页
TemplateList.vue:114 TemplateList: 接收到新的模板数据: 10
TemplateCard.vue:240 template: Proxy(Object) {templateId: 732, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.redpacket', templateName: '测试：弹窗', …}
TemplateCard.vue:249 原始 pages: Proxy(Array) {0: {…}}
TemplateCard.vue:332 使用第一页的内容: Proxy(Object) {contents: Array(6), pageId: 782}
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: redpacket
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'RedPacketTemplateRenderer', props: {…}, emits: Array(2), __hmrId: 'fb9e24ca', setup: ƒ, …}
RedPacketTemplateRenderer.vue:290 RedPacketTemplateRenderer - shouldAddFocusClass: false for content: 5942
ImageElement.vue:106 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.redpacket', templateName: '测试：弹窗', 判断结果: false}
ImageElement.vue:193 ImageElement - 不是轮播图模板，显示普通图片
ImageElement.vue:106 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.redpacket', templateName: '测试：弹窗', 判断结果: false}
ImageElement.vue:193 ImageElement - 不是轮播图模板，显示普通图片
TemplateCard.vue:240 template: Proxy(Object) {templateId: 731, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.imageandtext', templateName: '测试图文模板复制', …}
TemplateCard.vue:249 原始 pages: Proxy(Array) {0: {…}}
TemplateCard.vue:332 使用第一页的内容: Proxy(Object) {contents: Array(4), pageId: 781}
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: imageandtext
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'StandardTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '6fb42a47', setup: ƒ, …}
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
ImageElement.vue:106 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.imageandtext', templateName: '测试图文模板复制', 判断结果: false}
ImageElement.vue:193 ImageElement - 不是轮播图模板，显示普通图片
TemplateCard.vue:240 template: Proxy(Object) {templateId: 730, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.imageandtext', templateName: '测试图文', …}
TemplateCard.vue:249 原始 pages: Proxy(Array) {0: {…}}
TemplateCard.vue:332 使用第一页的内容: Proxy(Object) {contents: Array(4), pageId: 780}
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: imageandtext
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'StandardTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '6fb42a47', setup: ƒ, …}
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
ImageElement.vue:106 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.imageandtext', templateName: '测试图文', 判断结果: false}
ImageElement.vue:193 ImageElement - 不是轮播图模板，显示普通图片
TemplateCard.vue:240 template: Proxy(Object) {templateId: 729, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.imageandtext', templateName: '测试图文', …}
TemplateCard.vue:249 原始 pages: Proxy(Array) {0: {…}}
TemplateCard.vue:332 使用第一页的内容: Proxy(Object) {contents: Array(4), pageId: 779}
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: imageandtext
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'StandardTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '6fb42a47', setup: ƒ, …}
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
ImageElement.vue:106 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.imageandtext', templateName: '测试图文', 判断结果: false}
ImageElement.vue:193 ImageElement - 不是轮播图模板，显示普通图片
TemplateCard.vue:240 template: Proxy(Object) {templateId: 728, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.imageandtext', templateName: 'ces', …}
TemplateCard.vue:249 原始 pages: Proxy(Array) {0: {…}}
TemplateCard.vue:332 使用第一页的内容: Proxy(Object) {contents: Array(4), pageId: 778}
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: imageandtext
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'StandardTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '6fb42a47', setup: ƒ, …}
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
ImageElement.vue:106 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.imageandtext', templateName: 'ces', 判断结果: false}
ImageElement.vue:193 ImageElement - 不是轮播图模板，显示普通图片
TemplateCard.vue:240 template: Proxy(Object) {templateId: 723, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.imageandtext', templateName: 'ces', …}
TemplateCard.vue:249 原始 pages: Proxy(Array) {0: {…}}
TemplateCard.vue:332 使用第一页的内容: Proxy(Object) {contents: Array(4), pageId: 773}
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: imageandtext
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'StandardTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '6fb42a47', setup: ƒ, …}
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
ImageElement.vue:106 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.imageandtext', templateName: 'ces', 判断结果: false}
ImageElement.vue:193 ImageElement - 不是轮播图模板，显示普通图片
TemplateCard.vue:240 template: Proxy(Object) {templateId: 722, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateCard.vue:249 原始 pages: Proxy(Array) {0: {…}}
TemplateCard.vue:332 使用第一页的内容: Proxy(Object) {contents: Array(22), pageId: 772}
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: couponproduct
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'CouponProductTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '43bab64b', setup: ƒ, …}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplate.js:48 CouponProductTemplate - 处理券+商品内容数据，输入contents: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 1: {index: 0, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100926233510024.jpeg'}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 2: {index: 1, type: 'text', content: '编辑文本，最多12个中文字', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 3: {index: 2, type: 'text', content: '编辑文本，最多17个中文字', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 4: {index: 3, type: 'text', content: '99', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 5: {index: 4, type: 'text', content: '编辑文本，最多11个字', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 6: {index: 5, type: 'text', content: '编辑文本，最多32个中文字;编辑文本，最多32个中文字', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 7: {index: 6, type: 'button', content: '领', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 8: {index: 7, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100926233510024.jpeg'}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 9: {index: 8, type: 'text', content: '编辑文本，最多28个中文字', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 10: {index: 9, type: 'text', content: '编辑文本', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 11: {index: 10, type: 'text', content: '100.00', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 12: {index: 11, type: 'button', content: '立即购买', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 13: {index: 12, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100925014010023.jpeg'}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 14: {index: 13, type: 'text', content: '编辑文本，最多28个中文字', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 15: {index: 14, type: 'text', content: '编辑文本', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 16: {index: 15, type: 'text', content: '100.00', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 17: {index: 16, type: 'button', content: '立即购买', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 18: {index: 17, type: 'image', content: null, src: '/aim_files/A173440270073611/M175100923797710022.png'}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 19: {index: 18, type: 'text', content: '编辑文本，最多28个中文字', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 20: {index: 19, type: 'text', content: '编辑文本', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 21: {index: 20, type: 'text', content: '100.00', src: null}
CouponProductTemplate.js:64 CouponProductTemplate - 映射 positionNumber 22: {index: 21, type: 'button', content: '立即购买', src: null}
CouponProductTemplate.js:75 CouponProductTemplate - contentMap 键值: (22) [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]
CouponProductTemplate.js:119 处理券金额数据: Proxy(Object) {contentId: 5884, pageId: 772, templateId: 722, type: 'text', content: '99', …}
CouponProductTemplate.js:144 CouponProductTemplate - 开始处理商品数据，配置: (3) [{…}, {…}, {…}]
CouponProductTemplate.js:162 CouponProductTemplate - 处理商品 1，起始位置: 8
CouponProductTemplate.js:171 CouponProductTemplate - 商品 1 图片 (位置8): /aim_files/A173440270073611/M175100926233510024.jpeg
CouponProductTemplate.js:179 CouponProductTemplate - 商品 1 标题 (位置9): 编辑文本，最多28个中文字
CouponProductTemplate.js:187 CouponProductTemplate - 商品 1 标签 (位置10): 编辑文本
CouponProductTemplate.js:197 CouponProductTemplate - 商品 1 价格 (位置11): 100.00
CouponProductTemplate.js:208 CouponProductTemplate - 商品 1 按钮 (位置12): 立即购买
CouponProductTemplate.js:213 CouponProductTemplate - 商品 1 最终数据: {image: '/aim_files/A173440270073611/M175100926233510024.jpeg', title: '编辑文本，最多28个中文字', tag: '编辑文本', price: '100.00', buttonText: '立即购买'}
CouponProductTemplate.js:224 CouponProductTemplate - 商品 1 已添加到结果中
CouponProductTemplate.js:162 CouponProductTemplate - 处理商品 2，起始位置: 13
CouponProductTemplate.js:171 CouponProductTemplate - 商品 2 图片 (位置13): /aim_files/A173440270073611/M175100925014010023.jpeg
CouponProductTemplate.js:179 CouponProductTemplate - 商品 2 标题 (位置14): 编辑文本，最多28个中文字
CouponProductTemplate.js:187 CouponProductTemplate - 商品 2 标签 (位置15): 编辑文本
CouponProductTemplate.js:197 CouponProductTemplate - 商品 2 价格 (位置16): 100.00
CouponProductTemplate.js:208 CouponProductTemplate - 商品 2 按钮 (位置17): 立即购买
CouponProductTemplate.js:213 CouponProductTemplate - 商品 2 最终数据: {image: '/aim_files/A173440270073611/M175100925014010023.jpeg', title: '编辑文本，最多28个中文字', tag: '编辑文本', price: '100.00', buttonText: '立即购买'}
CouponProductTemplate.js:224 CouponProductTemplate - 商品 2 已添加到结果中
CouponProductTemplate.js:162 CouponProductTemplate - 处理商品 3，起始位置: 18
CouponProductTemplate.js:171 CouponProductTemplate - 商品 3 图片 (位置18): /aim_files/A173440270073611/M175100923797710022.png
CouponProductTemplate.js:179 CouponProductTemplate - 商品 3 标题 (位置19): 编辑文本，最多28个中文字
CouponProductTemplate.js:187 CouponProductTemplate - 商品 3 标签 (位置20): 编辑文本
CouponProductTemplate.js:197 CouponProductTemplate - 商品 3 价格 (位置21): 100.00
CouponProductTemplate.js:208 CouponProductTemplate - 商品 3 按钮 (位置22): 立即购买
CouponProductTemplate.js:213 CouponProductTemplate - 商品 3 最终数据: {image: '/aim_files/A173440270073611/M175100923797710022.png', title: '编辑文本，最多28个中文字', tag: '编辑文本', price: '100.00', buttonText: '立即购买'}
CouponProductTemplate.js:224 CouponProductTemplate - 商品 3 已添加到结果中
CouponProductTemplate.js:230 CouponProductTemplate - 券+商品数据处理完成: {headerProduct: {…}, coupon: {…}, products: Array(3)}
CouponProductTemplateRenderer.vue:160 CouponProductTemplateRenderer - 从contents提取的数据: {headerProduct: {…}, coupon: {…}, products: Array(3)}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 3 总商品: 3
TemplateCard.vue:240 template: Proxy(Object) {templateId: 721, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.cardVoucher', templateName: '测试：点击事件', …}
TemplateCard.vue:249 原始 pages: Proxy(Array) {0: {…}}
TemplateCard.vue:332 使用第一页的内容: Proxy(Object) {contents: Array(8), pageId: 771}
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: cardvoucher
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'CardVoucherTemplateRenderer', props: {…}, emits: Array(2), __hmrId: 'e06b0bba', setup: ƒ, …}
CardVoucherTemplateRenderer.vue:154 内容匹配结果： {amount: Proxy(Object), conditionTag: Proxy(Object), condition: Proxy(Object), description: Proxy(Object), validity: Proxy(Object), …}
CardVoucherTemplateRenderer.vue:84 CardVoucherRenderer - 所有内容: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}}
CardVoucherTemplateRenderer.vue:85 CardVoucherRenderer - 背景内容: Proxy(Object) {contentId: 87, pageId: 771, templateId: 15, type: 'background', content: '<null>', …}
TemplateCard.vue:240 template: Proxy(Object) {templateId: 720, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.cardVoucher', templateName: '测试', …}
TemplateCard.vue:249 原始 pages: Proxy(Array) {0: {…}}
TemplateCard.vue:332 使用第一页的内容: Proxy(Object) {contents: Array(8), pageId: 770}
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: cardvoucher
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'CardVoucherTemplateRenderer', props: {…}, emits: Array(2), __hmrId: 'e06b0bba', setup: ƒ, …}
CardVoucherTemplateRenderer.vue:154 内容匹配结果： {amount: Proxy(Object), conditionTag: Proxy(Object), condition: Proxy(Object), description: Proxy(Object), validity: Proxy(Object), …}
CardVoucherTemplateRenderer.vue:84 CardVoucherRenderer - 所有内容: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}}
CardVoucherTemplateRenderer.vue:85 CardVoucherRenderer - 背景内容: Proxy(Object) {contentId: 87, pageId: 770, templateId: 15, type: 'background', content: '<null>', …}
TemplateCard.vue:240 template: Proxy(Object) {templateId: 719, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.cardVoucher', templateName: '测试：单卡券', …}
TemplateCard.vue:249 原始 pages: Proxy(Array) {0: {…}}
TemplateCard.vue:332 使用第一页的内容: Proxy(Object) {contents: Array(8), pageId: 769}
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: cardvoucher
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'CardVoucherTemplateRenderer', props: {…}, emits: Array(2), __hmrId: 'e06b0bba', setup: ƒ, …}
CardVoucherTemplateRenderer.vue:154 内容匹配结果： {amount: Proxy(Object), conditionTag: Proxy(Object), condition: Proxy(Object), description: Proxy(Object), validity: Proxy(Object), …}
CardVoucherTemplateRenderer.vue:84 CardVoucherRenderer - 所有内容: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}}
CardVoucherTemplateRenderer.vue:85 CardVoucherRenderer - 背景内容: Proxy(Object) {contentId: 87, pageId: 769, templateId: 15, type: 'background', content: '<null>', …}
RedPacketTemplateRenderer.vue:164 红包模板更新内容: {contentId: 5940, pageId: 782, templateId: 732, type: 'text', content: '测试红包名称', …}
RedPacketTemplateRenderer.vue:170 RedPacketTemplateRenderer - 用户输入内容，立即移除empty-focused类名
RedPacketTemplateRenderer.vue:164 红包模板更新内容: {contentId: 5941, pageId: 782, templateId: 732, type: 'text', content: '测试标题', …}
RedPacketTemplateRenderer.vue:170 RedPacketTemplateRenderer - 用户输入内容，立即移除empty-focused类名
RedPacketTemplateRenderer.vue:164 红包模板更新内容: {contentId: 5942, pageId: 782, templateId: 732, type: 'text', content: '测试文本', …}
RedPacketTemplateRenderer.vue:170 RedPacketTemplateRenderer - 用户输入内容，立即移除empty-focused类名
index.vue:819 获取模板数据用于复制: {templateId: 722, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
index.vue:826 从模板数据中获取subType: 1
index.vue:948 处理后的复制模板数据: {templateId: undefined, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
index.vue:825 [Vue warn]: Invalid prop: type check failed for prop "subType". Expected String with value "1", got Number with value 1. 
  at <MediaCategory dir-type=5 sub-type=1 onCategoryChange=fn<handleCategoryChange>  ... > 
  at <Index>
warn$1 @ chunk-CAGAHDR2.js?v=0ed09a29:2116
validateProp @ chunk-CAGAHDR2.js?v=0ed09a29:6468
validateProps @ chunk-CAGAHDR2.js?v=0ed09a29:6440
updateProps @ chunk-CAGAHDR2.js?v=0ed09a29:6237
updateComponentPreRender @ chunk-CAGAHDR2.js?v=0ed09a29:7545
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7467
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
patchBlockChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7136
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7054
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
runIfDirty @ chunk-CAGAHDR2.js?v=0ed09a29:519
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
flushJobs @ chunk-CAGAHDR2.js?v=0ed09a29:2471
Promise.then
queueFlush @ chunk-CAGAHDR2.js?v=0ed09a29:2385
queueJob @ chunk-CAGAHDR2.js?v=0ed09a29:2380
effect2.scheduler @ chunk-CAGAHDR2.js?v=0ed09a29:7532
trigger @ chunk-CAGAHDR2.js?v=0ed09a29:509
endBatch @ chunk-CAGAHDR2.js?v=0ed09a29:567
notify @ chunk-CAGAHDR2.js?v=0ed09a29:827
trigger @ chunk-CAGAHDR2.js?v=0ed09a29:801
set value @ chunk-CAGAHDR2.js?v=0ed09a29:1673
copyTemplate @ index.vue:825
await in copyTemplate
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleCopyTemplate @ TemplateList.vue:203
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
copyTemplate @ TemplateCard.vue:477
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleClick @ chunk-5CY6DC63.js?v=0ed09a29:7405
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
invoker @ chunk-CAGAHDR2.js?v=0ed09a29:11202
index.vue:825 [Vue warn]: Invalid prop: type check failed for prop "subType". Expected String with value "1", got Number with value 1. 
  at <TemplateEditor id="new-template-editor" ref="templateEditorRef" app-key="A173440270073611"  ... > 
  at <Index>
warn$1 @ chunk-CAGAHDR2.js?v=0ed09a29:2116
validateProp @ chunk-CAGAHDR2.js?v=0ed09a29:6468
validateProps @ chunk-CAGAHDR2.js?v=0ed09a29:6440
updateProps @ chunk-CAGAHDR2.js?v=0ed09a29:6237
updateComponentPreRender @ chunk-CAGAHDR2.js?v=0ed09a29:7545
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7467
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
updateComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7342
processComponent @ chunk-CAGAHDR2.js?v=0ed09a29:7277
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6782
patchBlockChildren @ chunk-CAGAHDR2.js?v=0ed09a29:7136
patchElement @ chunk-CAGAHDR2.js?v=0ed09a29:7054
processElement @ chunk-CAGAHDR2.js?v=0ed09a29:6913
patch @ chunk-CAGAHDR2.js?v=0ed09a29:6770
componentUpdateFn @ chunk-CAGAHDR2.js?v=0ed09a29:7490
run @ chunk-CAGAHDR2.js?v=0ed09a29:481
runIfDirty @ chunk-CAGAHDR2.js?v=0ed09a29:519
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
flushJobs @ chunk-CAGAHDR2.js?v=0ed09a29:2471
Promise.then
queueFlush @ chunk-CAGAHDR2.js?v=0ed09a29:2385
queueJob @ chunk-CAGAHDR2.js?v=0ed09a29:2380
effect2.scheduler @ chunk-CAGAHDR2.js?v=0ed09a29:7532
trigger @ chunk-CAGAHDR2.js?v=0ed09a29:509
endBatch @ chunk-CAGAHDR2.js?v=0ed09a29:567
notify @ chunk-CAGAHDR2.js?v=0ed09a29:827
trigger @ chunk-CAGAHDR2.js?v=0ed09a29:801
set value @ chunk-CAGAHDR2.js?v=0ed09a29:1673
copyTemplate @ index.vue:825
await in copyTemplate
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleCopyTemplate @ TemplateList.vue:203
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
copyTemplate @ TemplateCard.vue:477
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
emit @ chunk-CAGAHDR2.js?v=0ed09a29:8466
（匿名） @ chunk-CAGAHDR2.js?v=0ed09a29:10175
handleClick @ chunk-5CY6DC63.js?v=0ed09a29:7405
callWithErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2263
callWithAsyncErrorHandling @ chunk-CAGAHDR2.js?v=0ed09a29:2270
invoker @ chunk-CAGAHDR2.js?v=0ed09a29:11202
TemplateEditor.vue:1089 TemplateEditor - 保存复制模板原始数据: Proxy(Object) {scene: '推送', useId: 2, smsExample: '信息', aimSmsSigns: Array(0), factoryInfos: 'huawei'}
TemplateEditor.vue:1096 TemplateEditor - 设置复制模式标识: true
TemplateFactory.js:67 传入的template: {templateId: 1, userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: {templateId: 2, userId: null, appKey: null, cardId: 'com.hbm.redpacket', templateName: '红包', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: redpacket
TemplateFactory.js:67 传入的template: {templateId: 3, userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multitext
TemplateFactory.js:67 传入的template: {templateId: 4, userId: null, appKey: null, cardId: 'com.hbm.carouse', templateName: '横滑', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: horizontalswipe
TemplateFactory.js:67 传入的template: {templateId: 5, userId: null, appKey: null, cardId: 'com.hbm.videoimageandtext', templateName: '视频图文', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: videoimageandtext
TemplateFactory.js:67 传入的template: {templateId: 6, userId: null, appKey: null, cardId: 'com.hbm.videoimageandtext2', templateName: '图文视频', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: videoimageandtext
TemplateFactory.js:67 传入的template: {templateId: 7, userId: null, appKey: null, cardId: 'com.hbm.pureText', templateName: '长文本', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: longtext
TemplateFactory.js:67 传入的template: {templateId: 8, userId: null, appKey: null, cardId: 'com.hbm.video', templateName: '视频', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: video
TemplateFactory.js:67 传入的template: {templateId: 9, userId: null, appKey: null, cardId: 'com.hbm.carouselImageSixteenToNine', templateName: '图片轮播16:9', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: carousel
TemplateFactory.js:67 传入的template: {templateId: 10, userId: null, appKey: null, cardId: 'com.hbm.carouselQuareImage', templateName: '图片轮播1:1', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: carousel
TemplateFactory.js:67 传入的template: {templateId: 11, userId: null, appKey: null, cardId: 'com.hbm.carouselVerticalImage', templateName: '图片轮播48:65', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: carousel
TemplateFactory.js:67 传入的template: {templateId: 12, userId: null, appKey: null, cardId: 'com.hbm.ecImageAndText', templateName: '电商', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: ecommerce
TemplateFactory.js:67 传入的template: {templateId: 13, userId: null, appKey: null, cardId: 'com.hbm.ecommerce', templateName: '电商(多商品)', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multiproduct
TemplateFactory.js:67 传入的template: {templateId: 15, userId: null, appKey: null, cardId: 'com.hbm.cardVoucher', templateName: '单卡券', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: cardvoucher
TemplateFactory.js:67 传入的template: {templateId: 16, userId: null, appKey: null, cardId: 'com.hbm.notification', templateName: '一般通知类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: notification
TemplateFactory.js:67 传入的template: {templateId: 17, userId: null, appKey: null, cardId: 'com.hbm.notification', templateName: '增强通知类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: notification
TemplateFactory.js:67 传入的template: {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:1129 TemplateEditor - 复制模式：查找匹配模板 {originalTemplateName: '测试电商模板', originalCardId: 'com.hbm.ecommerceCouponVertical.v2', templateDataTplType: null, availableTemplates: Array(17)}
TemplateEditor.vue:1151 TemplateEditor - 复制模式：找到匹配模板 Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', …}
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
CouponProductTemplate.js:14 CouponProductTemplate - 初始化模板内容: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
CouponProductTemplate.js:28 CouponProductTemplate - 原始contents: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductTemplate.js:38 CouponProductTemplate - 初始化后的contents: (22) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6657 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:7465 TemplateEditor - 提取券+商品数据，内容: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object), 8: Proxy(Object), 9: Proxy(Object), 10: Proxy(Object), 11: Proxy(Object), 12: Proxy(Object), 13: Proxy(Object), 14: Proxy(Object), 15: Proxy(Object), 16: Proxy(Object), 17: Proxy(Object), 18: Proxy(Object), 19: Proxy(Object), 20: Proxy(Object), 21: Proxy(Object)}
TemplateEditor.vue:7623 TemplateEditor - 券+商品数据提取成功: {headerProduct: {…}, coupon: {…}, products: Array(0)}
TemplateEditor.vue:6667 TemplateEditor - 从模板内容提取券+商品显示数据: {headerProduct: {…}, coupon: {…}, products: Array(0)}
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: couponproduct
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'CouponProductTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '43bab64b', setup: ƒ, …}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: {headerProduct: {…}, coupon: {…}, products: Array(0)}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 0 总商品: 0
TemplateEditor.vue:1183 TemplateEditor - 复制模式：解析模板内容
TemplateEditor.vue:1184 TemplateEditor - 复制模式：原始pages数据: [{…}]
TemplateEditor.vue:1212 TemplateEditor - 复制模式：已设置模板内容: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5881, type: 'image', actionType: 'OPEN_EMAIL', emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5887, type: 'button', actionType: 'COPY_PARAMETER', emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5888, type: 'image', actionType: null, emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5892, type: 'button', actionType: 'OPEN_SCHEDULE', emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5893, type: 'image', actionType: null, emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5897, type: 'button', actionType: 'OPEN_POPUP', emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5898, type: 'image', actionType: null, emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5902, type: 'button', actionType: 'OPEN_SMS', emailAddress: undefined, emailSubject: undefined, …}
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:1239 TemplateEditor - 复制模式：检测到券商品模板，初始化设置状态
TemplateEditor.vue:1240 TemplateEditor - 复制模式：当前selectedTemplateContents: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
TemplateEditor.vue:7465 TemplateEditor - 提取券+商品数据，内容: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
TemplateEditor.vue:7623 TemplateEditor - 券+商品数据提取成功: {headerProduct: {…}, coupon: {…}, products: Array(0)}
TemplateEditor.vue:1244 TemplateEditor - 复制模式：提取的券商品数据: {headerProduct: {…}, coupon: {…}, products: Array(0)}
TemplateEditor.vue:1248 TemplateEditor - 复制模式：券商品设置状态已初始化
TemplateEditor.vue:1252 TemplateEditor - 复制模式：处理点击事件数据
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5881, type: 'image', actionType: 'OPEN_EMAIL', actionJson: '{"subject":"2","body":"3","target":"<EMAIL>"}', actionJsonType: 'string'}
TemplateEditor.vue:1267 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {subject: '2', body: '3', target: '<EMAIL>'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5881, type: 'image', finalActionType: 'OPEN_EMAIL', hasActionJson: true}
TemplateEditor.vue:1309 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5881, type: 'image', actionType: 'OPEN_EMAIL', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1318 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5881,
  "type": "image",
  "actionType": "OPEN_EMAIL",
  "actionUrl": "<EMAIL>",
  "actionPath": "2",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "<EMAIL>",
  "emailSubject": "2",
  "emailBody": "3",
  "scheduleTitle": "<EMAIL>",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "<EMAIL>",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "<EMAIL>"
}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5887, type: 'button', actionType: 'COPY_PARAMETER', actionJson: '{"target":"123"}', actionJsonType: 'string'}
TemplateEditor.vue:1267 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {target: '123'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5887, type: 'button', finalActionType: 'COPY_PARAMETER', hasActionJson: true}
TemplateEditor.vue:1309 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5887, type: 'button', actionType: 'COPY_PARAMETER', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1318 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5887,
  "type": "button",
  "actionType": "COPY_PARAMETER",
  "actionUrl": "123",
  "actionPath": "",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "123",
  "emailSubject": "",
  "emailBody": "",
  "scheduleTitle": "123",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "123",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "123"
}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5888, type: 'image', actionType: null, actionJson: null, actionJsonType: 'object'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5888, type: 'image', finalActionType: null, hasActionJson: false}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5892, type: 'button', actionType: 'OPEN_SCHEDULE', actionJson: '{"description":"2","beginTime":"2025-07-01 00:00:00","endTime":"2025-07-31 00:00:00","target":"1"}', actionJsonType: 'string'}
TemplateEditor.vue:1267 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {description: '2', beginTime: '2025-07-01 00:00:00', endTime: '2025-07-31 00:00:00', target: '1'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5892, type: 'button', finalActionType: 'OPEN_SCHEDULE', hasActionJson: true}
TemplateEditor.vue:1309 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5892, type: 'button', actionType: 'OPEN_SCHEDULE', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1318 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5892,
  "type": "button",
  "actionType": "OPEN_SCHEDULE",
  "actionUrl": "1",
  "actionPath": "2",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "1",
  "emailSubject": "",
  "emailBody": "",
  "scheduleTitle": "1",
  "scheduleContent": "2",
  "scheduleStartTimeString": "2025-07-01 00:00:00",
  "scheduleEndTimeString": "2025-07-31 00:00:00",
  "popupTitle": "1",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "1"
}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5893, type: 'image', actionType: null, actionJson: null, actionJsonType: 'object'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5893, type: 'image', finalActionType: null, hasActionJson: false}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5897, type: 'button', actionType: 'OPEN_POPUP', actionJson: '{"mode":0,"textButton":"3","content":"2","target":"1"}', actionJsonType: 'string'}
TemplateEditor.vue:1267 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {mode: 0, textButton: '3', content: '2', target: '1'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5897, type: 'button', finalActionType: 'OPEN_POPUP', hasActionJson: true}
TemplateEditor.vue:1309 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5897, type: 'button', actionType: 'OPEN_POPUP', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1318 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5897,
  "type": "button",
  "actionType": "OPEN_POPUP",
  "actionUrl": "1",
  "actionPath": "2",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "1",
  "emailSubject": "",
  "emailBody": "",
  "scheduleTitle": "1",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "1",
  "popupContent": "2",
  "popupButtonText": "3",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "1"
}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5898, type: 'image', actionType: null, actionJson: null, actionJsonType: 'object'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5898, type: 'image', finalActionType: null, hasActionJson: false}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5902, type: 'button', actionType: 'OPEN_SMS', actionJson: '{"body":"123","target":"13600001111"}', actionJsonType: 'string'}
TemplateEditor.vue:1267 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {body: '123', target: '13600001111'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5902, type: 'button', finalActionType: 'OPEN_SMS', hasActionJson: true}
clickEventManager.js:611 SMS extractActionFields: {actionJson: {…}, extractedActionUrl: '13600001111', extractedActionPath: '123', bodyValue: '123'}
TemplateEditor.vue:1309 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5902, type: 'button', actionType: 'OPEN_SMS', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1318 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5902,
  "type": "button",
  "actionType": "OPEN_SMS",
  "actionUrl": "13600001111",
  "actionPath": "123",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "13600001111",
  "emailSubject": "",
  "emailBody": "123",
  "scheduleTitle": "13600001111",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "13600001111",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "13600001111"
}
TemplateEditor.vue:1355 TemplateEditor - 复制模式设置完成，最终确认标识: true
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6657 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6661 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(0)}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(0)}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 0 总商品: 0
