index.vue:819 获取模板数据用于复制: {templateId: 722, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
index.vue:826 从模板数据中获取subType: 1
index.vue:948 处理后的复制模板数据: {templateId: undefined, userId: 1, appKey: 'A173440270073611', cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateEditor.vue:1089 TemplateEditor - 保存复制模板原始数据: Proxy(Object) {scene: '推送', useId: 2, smsExample: '信息', aimSmsSigns: Array(0), factoryInfos: 'huawei'}
TemplateEditor.vue:1096 TemplateEditor - 设置复制模式标识: true
TemplateFactory.js:67 传入的template: {templateId: 1, userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: {templateId: 2, userId: null, appKey: null, cardId: 'com.hbm.redpacket', templateName: '红包', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: redpacket
TemplateFactory.js:67 传入的template: {templateId: 3, userId: null, appKey: null, cardId: 'com.hbm.standardimageandtext', templateName: '多图文', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multitext
TemplateFactory.js:67 传入的template: {templateId: 4, userId: null, appKey: null, cardId: 'com.hbm.carouse', templateName: '横滑', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: horizontalswipe
TemplateFactory.js:67 传入的template: {templateId: 5, userId: null, appKey: null, cardId: 'com.hbm.videoimageandtext', templateName: '视频图文', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: videoimageandtext
TemplateFactory.js:67 传入的template: {templateId: 6, userId: null, appKey: null, cardId: 'com.hbm.videoimageandtext2', templateName: '图文视频', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: videoimageandtext
TemplateFactory.js:67 传入的template: {templateId: 7, userId: null, appKey: null, cardId: 'com.hbm.pureText', templateName: '长文本', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: longtext
TemplateFactory.js:67 传入的template: {templateId: 8, userId: null, appKey: null, cardId: 'com.hbm.video', templateName: '视频', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: video
TemplateFactory.js:67 传入的template: {templateId: 9, userId: null, appKey: null, cardId: 'com.hbm.carouselImageSixteenToNine', templateName: '图片轮播16:9', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: carousel
TemplateFactory.js:67 传入的template: {templateId: 10, userId: null, appKey: null, cardId: 'com.hbm.carouselQuareImage', templateName: '图片轮播1:1', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: carousel
TemplateFactory.js:67 传入的template: {templateId: 11, userId: null, appKey: null, cardId: 'com.hbm.carouselVerticalImage', templateName: '图片轮播48:65', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: carousel
TemplateFactory.js:67 传入的template: {templateId: 12, userId: null, appKey: null, cardId: 'com.hbm.ecImageAndText', templateName: '电商', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: ecommerce
TemplateFactory.js:67 传入的template: {templateId: 13, userId: null, appKey: null, cardId: 'com.hbm.ecommerce', templateName: '电商(多商品)', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: multiproduct
TemplateFactory.js:67 传入的template: {templateId: 15, userId: null, appKey: null, cardId: 'com.hbm.cardVoucher', templateName: '单卡券', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: cardvoucher
TemplateFactory.js:67 传入的template: {templateId: 16, userId: null, appKey: null, cardId: 'com.hbm.notification', templateName: '一般通知类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: notification
TemplateFactory.js:67 传入的template: {templateId: 17, userId: null, appKey: null, cardId: 'com.hbm.notification', templateName: '增强通知类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: notification
TemplateFactory.js:67 传入的template: {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:1129 TemplateEditor - 复制模式：查找匹配模板 {originalTemplateName: '测试电商模板', originalCardId: 'com.hbm.ecommerceCouponVertical.v2', templateDataTplType: null, availableTemplates: Array(17)}
TemplateEditor.vue:1151 TemplateEditor - 复制模式：找到匹配模板 Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '券+商品(竖版)', …}
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
CouponProductTemplate.js:14 CouponProductTemplate - 初始化模板内容: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
CouponProductTemplate.js:28 CouponProductTemplate - 原始contents: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
CouponProductTemplate.js:38 CouponProductTemplate - 初始化后的contents: (22) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6657 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:7465 TemplateEditor - 提取券+商品数据，内容: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object), 7: Proxy(Object), 8: Proxy(Object), 9: Proxy(Object), 10: Proxy(Object), 11: Proxy(Object), 12: Proxy(Object), 13: Proxy(Object), 14: Proxy(Object), 15: Proxy(Object), 16: Proxy(Object), 17: Proxy(Object), 18: Proxy(Object), 19: Proxy(Object), 20: Proxy(Object), 21: Proxy(Object)}
TemplateEditor.vue:7623 TemplateEditor - 券+商品数据提取成功: {headerProduct: {…}, coupon: {…}, products: Array(0)}
TemplateEditor.vue:6667 TemplateEditor - 从模板内容提取券+商品显示数据: {headerProduct: {…}, coupon: {…}, products: Array(0)}
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: couponproduct
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'CouponProductTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '43bab64b', setup: ƒ, …}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: {headerProduct: {…}, coupon: {…}, products: Array(0)}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 0 总商品: 0
TemplateEditor.vue:1183 TemplateEditor - 复制模式：解析模板内容
TemplateEditor.vue:1184 TemplateEditor - 复制模式：原始pages数据: [{…}]
TemplateEditor.vue:1212 TemplateEditor - 复制模式：已设置模板内容: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5881, type: 'image', actionType: 'OPEN_EMAIL', emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5887, type: 'button', actionType: 'COPY_PARAMETER', emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5888, type: 'image', actionType: null, emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5892, type: 'button', actionType: 'OPEN_SCHEDULE', emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5893, type: 'image', actionType: null, emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5897, type: 'button', actionType: 'OPEN_POPUP', emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5898, type: 'image', actionType: null, emailAddress: undefined, emailSubject: undefined, …}
TemplateEditor.vue:1217 TemplateEditor - 复制模式：最终内容检查: {contentId: 5902, type: 'button', actionType: 'OPEN_SMS', emailAddress: undefined, emailSubject: undefined, …}
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:1239 TemplateEditor - 复制模式：检测到券商品模板，初始化设置状态
TemplateEditor.vue:1240 TemplateEditor - 复制模式：当前selectedTemplateContents: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
TemplateEditor.vue:7465 TemplateEditor - 提取券+商品数据，内容: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}, 17: {…}, 18: {…}, 19: {…}, 20: {…}, 21: {…}}
TemplateEditor.vue:7623 TemplateEditor - 券+商品数据提取成功: {headerProduct: {…}, coupon: {…}, products: Array(0)}
TemplateEditor.vue:1244 TemplateEditor - 复制模式：提取的券商品数据: {headerProduct: {…}, coupon: {…}, products: Array(0)}
TemplateEditor.vue:1248 TemplateEditor - 复制模式：券商品设置状态已初始化
TemplateEditor.vue:1252 TemplateEditor - 复制模式：处理点击事件数据
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5881, type: 'image', actionType: 'OPEN_EMAIL', actionJson: '{"subject":"2","body":"3","target":"<EMAIL>"}', actionJsonType: 'string'}
TemplateEditor.vue:1267 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {subject: '2', body: '3', target: '<EMAIL>'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5881, type: 'image', finalActionType: 'OPEN_EMAIL', hasActionJson: true}
TemplateEditor.vue:1309 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5881, type: 'image', actionType: 'OPEN_EMAIL', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1318 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5881,
  "type": "image",
  "actionType": "OPEN_EMAIL",
  "actionUrl": "<EMAIL>",
  "actionPath": "2",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "<EMAIL>",
  "emailSubject": "2",
  "emailBody": "3",
  "scheduleTitle": "<EMAIL>",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "<EMAIL>",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "<EMAIL>"
}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5887, type: 'button', actionType: 'COPY_PARAMETER', actionJson: '{"target":"123"}', actionJsonType: 'string'}
TemplateEditor.vue:1267 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {target: '123'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5887, type: 'button', finalActionType: 'COPY_PARAMETER', hasActionJson: true}
TemplateEditor.vue:1309 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5887, type: 'button', actionType: 'COPY_PARAMETER', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1318 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5887,
  "type": "button",
  "actionType": "COPY_PARAMETER",
  "actionUrl": "123",
  "actionPath": "",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "123",
  "emailSubject": "",
  "emailBody": "",
  "scheduleTitle": "123",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "123",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "123"
}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5888, type: 'image', actionType: null, actionJson: null, actionJsonType: 'object'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5888, type: 'image', finalActionType: null, hasActionJson: false}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5892, type: 'button', actionType: 'OPEN_SCHEDULE', actionJson: '{"description":"2","beginTime":"2025-07-01 00:00:00","endTime":"2025-07-31 00:00:00","target":"1"}', actionJsonType: 'string'}
TemplateEditor.vue:1267 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {description: '2', beginTime: '2025-07-01 00:00:00', endTime: '2025-07-31 00:00:00', target: '1'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5892, type: 'button', finalActionType: 'OPEN_SCHEDULE', hasActionJson: true}
TemplateEditor.vue:1309 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5892, type: 'button', actionType: 'OPEN_SCHEDULE', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1318 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5892,
  "type": "button",
  "actionType": "OPEN_SCHEDULE",
  "actionUrl": "1",
  "actionPath": "2",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "1",
  "emailSubject": "",
  "emailBody": "",
  "scheduleTitle": "1",
  "scheduleContent": "2",
  "scheduleStartTimeString": "2025-07-01 00:00:00",
  "scheduleEndTimeString": "2025-07-31 00:00:00",
  "popupTitle": "1",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "1"
}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5893, type: 'image', actionType: null, actionJson: null, actionJsonType: 'object'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5893, type: 'image', finalActionType: null, hasActionJson: false}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5897, type: 'button', actionType: 'OPEN_POPUP', actionJson: '{"mode":0,"textButton":"3","content":"2","target":"1"}', actionJsonType: 'string'}
TemplateEditor.vue:1267 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {mode: 0, textButton: '3', content: '2', target: '1'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5897, type: 'button', finalActionType: 'OPEN_POPUP', hasActionJson: true}
TemplateEditor.vue:1309 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5897, type: 'button', actionType: 'OPEN_POPUP', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1318 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5897,
  "type": "button",
  "actionType": "OPEN_POPUP",
  "actionUrl": "1",
  "actionPath": "2",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "1",
  "emailSubject": "",
  "emailBody": "",
  "scheduleTitle": "1",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "1",
  "popupContent": "2",
  "popupButtonText": "3",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "1"
}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5898, type: 'image', actionType: null, actionJson: null, actionJsonType: 'object'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5898, type: 'image', finalActionType: null, hasActionJson: false}
TemplateEditor.vue:1255 TemplateEditor - 复制模式：处理内容点击事件: {contentId: 5902, type: 'button', actionType: 'OPEN_SMS', actionJson: '{"body":"123","target":"13600001111"}', actionJsonType: 'string'}
TemplateEditor.vue:1267 TemplateEditor - 复制模式：第一次解析actionJson成功: Proxy(Object) {body: '123', target: '13600001111'}
TemplateEditor.vue:1285 TemplateEditor - 复制模式：最终actionType设置: {contentId: 5902, type: 'button', finalActionType: 'OPEN_SMS', hasActionJson: true}
clickEventManager.js:611 SMS extractActionFields: {actionJson: {…}, extractedActionUrl: '13600001111', extractedActionPath: '123', bodyValue: '123'}
TemplateEditor.vue:1309 TemplateEditor - 复制模式：使用统一方法提取字段: {contentId: 5902, type: 'button', actionType: 'OPEN_SMS', actionJson: Proxy(Object), extractedFields: {…}}
TemplateEditor.vue:1318 TemplateEditor - 复制模式：详细提取字段: {
  "contentId": 5902,
  "type": "button",
  "actionType": "OPEN_SMS",
  "actionUrl": "13600001111",
  "actionPath": "123",
  "packageName": "",
  "floorType": "0",
  "emailAddress": "13600001111",
  "emailSubject": "",
  "emailBody": "123",
  "scheduleTitle": "13600001111",
  "scheduleContent": "",
  "scheduleStartTimeString": "",
  "scheduleEndTimeString": "",
  "popupTitle": "13600001111",
  "popupContent": "",
  "popupButtonText": "",
  "copyType": "1",
  "selectedParamId": "",
  "fixedContent": "13600001111"
}
TemplateEditor.vue:1355 TemplateEditor - 复制模式设置完成，最终确认标识: true
TemplateFactory.js:67 传入的template: Proxy(Object) {templateId: 21, userId: null, appKey: null, cardId: 'com.hbm.ecommerceCouponVertical.v2', templateName: '测试电商模板', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: couponproduct
TemplateEditor.vue:6657 TemplateEditor - 计算券+商品显示数据
TemplateEditor.vue:6661 TemplateEditor - 使用券+商品设置状态数据: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(0)}
CouponProductTemplateRenderer.vue:143 CouponProductTemplateRenderer - 计算显示数据
CouponProductTemplateRenderer.vue:146 CouponProductTemplateRenderer - 使用父组件传递的数据: Proxy(Object) {headerProduct: {…}, coupon: {…}, products: Array(0)}
CouponProductTemplateRenderer.vue:172 CouponProductTemplateRenderer - 可见商品: 0 总商品: 0
