<template>
  <div
    :class="[
      elementClass,
      { 'selected': isSelected || isEditing, 'editable': editable },
      customClass
    ]"
    :contenteditable="editable"
    spellcheck="false"
    ref="contentElement"
    @input="handleContentEditableInput"
    @focus="handleFocus"
    @click="handleClick"
    @contextmenu="handleClick"
    @blur="handleBlur"
    @compositionstart="handleCompositionStart"
    @compositionend="handleCompositionEnd"
  >
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount ,inject } from 'vue';
import { useTemplateParam } from '@/composables/useTemplateParam';
import { useParamService } from '@/services/ParamService';
import { ElMessage } from 'element-plus';

const props = defineProps({
  content: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: false
  },
  customClass: {
    type: [String, Array],
    default: ''
  },
  usage: {
    type: String,
    default: 'editor'
  }
});

const emit = defineEmits(['update:content', 'select', 'focus', 'blur', 'input']);

// 状态变量
const contentElement = ref(null);
const isEditing = ref(false);
const isFirstClick = ref(true);
const isComposing = ref(false);
const hasEditedContent = ref(false);
const hasFirstFocus = ref(false);

// 获取参数服务实例
const paramService = useParamService();

// 使用新的参数管理组合函数
const { insertParam, setContent } = useTemplateParam({
  target: () => contentElement.value,
  panelId: () => `text-element-${props.content.id}`,
  onChange: (value) => {
    // 更新编辑内容
    const newContent = {
      ...props.content,
      editContent: value,
      content: value,
      isEditing: true,
      userEdited: true
    };
    
    hasEditedContent.value = true;
    emit('update:content', newContent);
  }
});

// 计算元素类名
const elementClass = computed(() => {
  if (!props.content) return '';
  
  const result = props.content.isTextTitle === 1 ? 'preview-title' : 
    (props.content.positionNumber === 2 && window.TEMPLATE_IS_REDPACKET) ? 'preview-name' : 'preview-desc';
  
  return result;
});



// 可编辑区域的输入处理
const handleContentEditableInput = (e) => {
  if (!props.editable || isComposing.value) return;
  
  // 获取当前内容
  const currentContent = e.target.innerHTML;
  
  
  // 更新编辑内容，标记为用户已编辑
  const newContent = { 
    ...props.content,
    editContent: currentContent,
    content: currentContent,
    isEditing: true,
    userEdited: currentContent.trim() !== ''  // 重要：标记为用户已编辑，防止被自动恢复
  };
  
  hasEditedContent.value = true;
  
  // 发出input事件，用于实时响应用户输入（如拼音输入）
  emit('input', newContent);
  emit('update:content', newContent);
};

// 处理参数插入
const handleParamInsert = async (paramText) => {
  try {

    if (!contentElement.value || !props.editable) {
      console.error('无法插入参数：编辑器不可用', {
        elementAvailable: !!contentElement.value,
        isEditable: props.editable
      });
      return false;
    }

    // 检查当前参数数量
    const currentParams = contentElement.value.querySelectorAll('input.j-btn, span.j-btn').length;
    if (currentParams >= 7) {
      ElMessage.warning('每个输入框最多可插入 7 个参数，当前输入框已达上限');
      return false;
    }


    // 强制确保元素处于编辑状态和选中状态
    if (!props.isSelected) {
      console.log('TextElement: 元素未选中，触发选中事件');
      emit('select', props.content);
      await nextTick(); // 等待选中状态更新
    }
    
    // 确保元素处于编辑状态
    isEditing.value = true;
    
    // 确保元素有焦点
    contentElement.value.focus();
    await nextTick(); // 等待焦点生效
    
    // 检查是否是复制模式
    const isCopyMode = window._isCopyMode || false;
    console.log('TextElement handleClick - 复制模式检查:', {
      isCopyMode,
      windowCopyMode: window._isCopyMode,
      contentId: props.content.contentId,
      contentType: props.content.type
    });

    // 如果是复制模式，不清空任何内容
    if (isCopyMode) {
      console.log('复制模式下，保持内容不变');
    } else {
      // 非复制模式下，检查当前内容是否是默认文本，如果是则清空
      const currentHTML = contentElement.value.innerHTML;
      const isDefaultText = currentHTML === getDefaultText() ||
                           currentHTML === props.content.originalContent ||
                           isDefaultContent();

      if (isDefaultText) {
        console.log('检测到默认文本，清空内容');
        contentElement.value.innerHTML = '';

        // 更新内容状态为编辑状态
        const newContent = {
          ...props.content,
          editContent: '',
          content: '',
          isEditing: true,
          userEdited: true
        };

        emit('update:content', newContent);
        await nextTick(); // 等待内容更新
      }
    }

    // 生成 panelId，如果 props.content.id 不存在则使用默认值
    const panelId = `text-element-${props.content.id || Date.now()}`;

    // 获取参数 ID
    let paramId = null;
    if (typeof paramText === 'object' && paramText.paramId) {
      paramId = paramText.paramId;
    } else if (typeof paramText === 'string' && paramText.includes('{#param')) {
      const match = paramText.match(/{#param(\d+)#}/);
      if (match && match[1]) {
        paramId = match[1];
      }
    }

    // 如果没有有效的参数 ID，在数量检查通过后从服务获取
    if (!paramId || isNaN(parseInt(paramId))) {
      paramId = paramService.getNextParamId(panelId);
    }

    console.log(`准备插入参数 ID: ${paramId}`);

    // 获取当前内容
    const finalHTML = contentElement.value.innerHTML;
    const isEmpty = !finalHTML || 
                   finalHTML === '<br>' || 
                   finalHTML === '<div><br></div>' ||
                   finalHTML.trim() === '' || 
                   finalHTML === '&nbsp;' ||
                   finalHTML === '<p><br></p>' ||
                   finalHTML === '<div></div>';
    
    console.log('最终内容检测:', { finalHTML, isEmpty });

    // 创建参数HTML字符串
    const paramHTML = `<input type="button" class="j-btn param-input" value="{#param${paramId}#}" data-param-id="${paramId}" readonly="readonly" data-panel-id="${panelId}">`;
    
    let newHTML = '';
    
    if (isEmpty) {
      // 如果内容为空，直接插入参数和空格
      newHTML = paramHTML + ' ';
      console.log('空内容：直接插入参数');
    } else {
      // 如果有内容，需要在光标位置插入
      const selection = window.getSelection();
      
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        
        // 确保选区在内容元素内
        if (contentElement.value.contains(range.commonAncestorContainer)) {
          try {
            // 创建一个临时的标记元素来标识插入位置
            const marker = document.createElement('span');
            marker.id = 'temp-insert-marker-' + Date.now();
            
            // 在选区位置插入标记
            range.insertNode(marker);
            
            // 获取包含标记的HTML
            const htmlWithMarker = contentElement.value.innerHTML;
            
            // 用参数HTML替换标记
            newHTML = htmlWithMarker.replace(
              `<span id="${marker.id}"></span>`, 
              paramHTML + ' '
            );
            
            console.log('在光标位置插入参数');
          } catch (rangeError) {
            console.warn('光标位置插入失败，追加到末尾:', rangeError);
            // 备用方案：追加到末尾
            newHTML = finalHTML + paramHTML + ' ';
          }
        } else {
          // 选区不在内容元素内，追加到末尾
          newHTML = finalHTML + paramHTML + ' ';
          console.log('选区不在内容元素内，追加到末尾');
        }
      } else {
        // 没有选区，追加到末尾
        newHTML = finalHTML + paramHTML + ' ';
        console.log('无选区，追加到末尾');
      }
    }

    // 直接设置HTML内容
    contentElement.value.innerHTML = newHTML;
    
    console.log('参数HTML插入完成:', newHTML);

    // 验证参数元素是否成功插入
    const insertedParam = contentElement.value.querySelector(`input[data-param-id="${paramId}"]`);
    if (!insertedParam) {
      console.error('参数元素插入验证失败');
      return false;
    }

    console.log('参数元素验证成功');

    // 更新内容状态
    const newContent = {
      ...props.content,
      content: newHTML,
      editContent: newHTML,
      isEditing: true,
      userEdited: true
    };

    // 发送内容更新事件
    emit('update:content', newContent);

    // 等待 DOM 更新完成
    await nextTick();

    // 设置光标位置
    try {
      // 重新获取参数元素（因为DOM可能已更新）
      const finalParamElement = contentElement.value.querySelector(`input[data-param-id="${paramId}"]`);
      
      if (finalParamElement) {
        const finalSelection = window.getSelection();
        const finalRange = document.createRange();
        
        // 查找参数后的空格
        const nextNode = finalParamElement.nextSibling;
        
        if (nextNode && nextNode.nodeType === Node.TEXT_NODE) {
          // 设置光标到空格后面
          const textContent = nextNode.textContent;
          const spaceIndex = textContent.indexOf(' ');
          if (spaceIndex >= 0 && spaceIndex < textContent.length - 1) {
            finalRange.setStart(nextNode, spaceIndex + 1);
          } else {
            finalRange.setStart(nextNode, textContent.length);
          }
          finalRange.collapse(true);
        } else {
          // 如果没有找到文本节点，尝试设置到参数后面
          try {
            finalRange.setStartAfter(finalParamElement);
            finalRange.collapse(true);
          } catch (afterError) {
            // 最后备用方案：设置到内容末尾
            finalRange.selectNodeContents(contentElement.value);
            finalRange.collapse(false);
          }
        }
        
        finalSelection.removeAllRanges();
        finalSelection.addRange(finalRange);
        console.log('光标设置成功');
      } else {
        console.warn('无法找到最终的参数元素，设置光标到末尾');
        const finalSelection = window.getSelection();
        const finalRange = document.createRange();
        finalRange.selectNodeContents(contentElement.value);
        finalRange.collapse(false);
        finalSelection.removeAllRanges();
        finalSelection.addRange(finalRange);
      }
    } catch (cursorError) {
      console.warn('设置光标位置失败:', cursorError);
      // 最简单的备用方案
      try {
        const backupSelection = window.getSelection();
        const backupRange = document.createRange();
        backupRange.selectNodeContents(contentElement.value);
        backupRange.collapse(false);
        backupSelection.removeAllRanges();
        backupSelection.addRange(backupRange);
        console.log('使用备用光标设置');
      } catch (backupError) {
        console.warn('备用光标设置失败:', backupError);
      }
    }

    // 记录参数使用和关联
    paramService.recordParamUsage(paramId);
    paramService.associateParamWithPanel(paramId, panelId);

    console.log('参数插入完成');
    return true;
  } catch (error) {
    console.error('插入参数失败:', error, error.stack);
    ElMessage.error('插入参数失败，请重试');
    return false;
  }
};

// 获取默认文本
const getDefaultText = () => {
  // 优先使用原始内容（接口返回的数据）
  if (props.content.originalContent) {
    return props.content.originalContent;
  }
  
  // 如果没有原始内容，使用当前内容
  if (props.content.content) {
    return props.content.content;
  }
  
  // 最后才使用硬编码的默认文本
  if (props.content.isTextTitle === 1) {
    return '编辑标题';
  } else if (props.content.positionNumber === 2 && window.TEMPLATE_IS_REDPACKET) {
    return '红包名称';
  } else if (props.content.pageLayout === 'left' && props.content.pageLines > 2) {
    // 通知类模板的左侧参数名称
    return '编辑名称';
  } else if (props.content.isTextTitle === 0 && props.content.positionNumber === 2) {
    return '编辑描述';
  } else {
    return '编辑文本';
  }
};

// 判断是否是默认内容
const isDefaultContent = () => {
  if (!props.content || !props.content.content) return true;

  const content = props.content.content.trim();

  // 如果有原始内容，判断当前内容是否与原始内容相同
  if (props.content.originalContent) {
    return content === props.content.originalContent.trim();
  }

  // 如果没有原始内容，只检查是否为空或标准默认文本
  return content === '' || content === '<br>' || content === '&nbsp;' || content.includes('编辑文本');
};

// 仅在可编辑模式下注入 currentTemplate
let currentTemplate = null;
if (props.editable) {
  currentTemplate = inject('currentTemplate');
}

// 处理获得焦点
const handleFocus = async () => {
  console.log('文本元素获得焦点');
  if (!props.editable) return;  // 非可编辑模式直接返回
  
  // 检查是否正在拖拽，如果是则不处理焦点事件
  if (window._isDragging) {
    console.log('检测到拖拽操作，延迟处理焦点');
    setTimeout(() => {
      if (contentElement.value && document.activeElement === contentElement.value && !window._isDragging) {
        // 如果拖拽结束后仍然有焦点，则正常处理
        console.log('拖拽结束，重新处理焦点');
        handleFocus();
      } else if (window._isDragging) {
        // 如果仍在拖拽，则移除焦点
        contentElement.value?.blur();
      }
    }, 100);
    return;
  }
  
  // 检查是否正在清除选择状态，如果是则延迟处理
  if (window._isClearingSelection) {
    console.log('检测到清除选择标记，延迟处理焦点');
    setTimeout(() => {
      if (contentElement.value && document.activeElement === contentElement.value) {
        contentElement.value.blur();
      }
    }, 50);
    return;
  }
  
  // 设置用户交互标记
  window._userInteractionFlag = true;
  setTimeout(() => {
    window._userInteractionFlag = false;
  }, 200);
  
  console.log('handleFocus - 内容:', props.content.content, '是否首次焦点:', !hasFirstFocus.value);

  isEditing.value = true;
  
  // 保存原始内容，用于后续恢复
  if (!props.content.originalContent) {
    props.content.originalContent = props.content.content;
  }

  // 判断是否是通知类模板的参数输入框 - 使用更准确的判断逻辑
  const isNotificationParam = props.content.content && 
    typeof props.content.content === 'string' &&
    /^\{#param\d+#\}$/.test(props.content.content.trim());

  console.log('handleFocus - 是否是参数输入框:', isNotificationParam);

  // 如果是通知类模板的参数输入框，不清空默认参数，但允许用户编辑
  if (isNotificationParam) {
    console.log('这是参数输入框，保持默认内容但允许编辑');

    try {
      await nextTick();
      const newContent = {
        ...props.content,
        isEditing: true
      };

      emit('update:content', newContent);
      emit('select', props.content);
      emit('focus', props.content);
    } catch (error) {
      console.error('参数输入框焦点处理错误:', error);
    }
    return;
  }

  // 检查是否是复制模式
  const isCopyMode = window._isCopyMode || false;
  console.log('TextElement handleFocus - 复制模式检查:', {
    isCopyMode,
    windowCopyMode: window._isCopyMode,
    contentId: props.content.contentId,
    contentType: props.content.type,
    hasFirstFocus: hasFirstFocus.value
  });

  // 如果是复制模式，不清空任何内容，只设置编辑状态
  if (isCopyMode) {
    console.log('复制模式下，保持内容不变');
    hasFirstFocus.value = true;

    try {
      await nextTick();
      const newContent = {
        ...props.content,
        isEditing: true
      };

      emit('update:content', newContent);
      emit('select', props.content);
      emit('focus', props.content);
    } catch (error) {
      console.error('复制模式焦点处理错误:', error);
    }
    return;
  }

  // 如果是首次获得焦点且是默认文本，则清空
  if (!hasFirstFocus.value && isDefaultContent()) {
    console.log('首次获得焦点，清空默认文本');
    
    try {
      // 清空默认文本逻辑
      const newContent = {
        ...props.content,
        editContent: '',
        content: '',
        isEditing: true,
        userEdited: true
      };
      
      hasEditedContent.value = true;
      hasFirstFocus.value = true;
      
      emit('update:content', newContent);
      
      await nextTick();
      
      if (contentElement.value) {
        contentElement.value.innerHTML = '';
        contentElement.value.focus();

        const range = document.createRange();
        const selection = window.getSelection();

        range.setStart(contentElement.value, 0);
        range.collapse(true);

        selection.removeAllRanges();
        selection.addRange(range);
      }
    } catch (error) {
      console.error('首次焦点处理错误:', error);
    }
  } else {
    // 如果不是首次焦点或不是默认内容，不清空，只设置编辑状态
    console.log('保持内容不变，只设置编辑状态');
    hasFirstFocus.value = true; // 标记已经获得过焦点
    
    try {
      await nextTick();
      const newContent = {
        ...props.content,
        isEditing: true
      };
      
      emit('update:content', newContent);
    } catch (error) {
      console.error('非首次焦点处理错误:', error);
    }
  }
  
  emit('select', props.content);
  emit('focus', props.content);
};

// 处理文本点击(左键和右键)
const handleClick = (event) => {
  if (!props.editable) return;

  // 检查是否正在拖拽，如果是则不处理点击事件
  if (window._isDragging) {
    console.log('检测到拖拽操作，跳过点击处理');
    event.preventDefault();
    event.stopPropagation();
    return;
  }

  // 检查是否正在清除选择状态，如果是则不处理点击事件
  if (window._isClearingSelection) {
    console.log('检测到清除选择标记，跳过点击处理');
    event.preventDefault();
    event.stopPropagation();
    return;
  }

  // 防止右键默认菜单
  if (event.type === 'contextmenu') {
    event.preventDefault();
  }

  // 设置用户交互标记
  window._userInteractionFlag = true;
  setTimeout(() => {
    window._userInteractionFlag = false;
  }, 200);

  // 确保触发选择事件
  console.log('TextElement: 发出select事件，内容:', props.content);
  emit('select', props.content);

  // 保存原始内容，用于后续恢复
  if (!props.content.originalContent) {
    props.content.originalContent = props.content.content || getDefaultText();
  }

  // 判断是否是通知类模板的参数输入框 - 使用更准确的判断逻辑
  const isNotificationParam = props.content.content && 
    typeof props.content.content === 'string' &&
    /^\{#param\d+#\}$/.test(props.content.content.trim());

  console.log('handleClick - 是否是参数输入框:', isNotificationParam, '内容:', props.content.content);

  // 如果是首次点击
  if (isFirstClick.value) {
    isFirstClick.value = false;
    isEditing.value = true;

    // 如果是通知类模板的参数输入框，不清空默认参数
    if (isNotificationParam) {
      console.log('这是参数输入框，保持默认内容不变');
      const newContent = { 
        ...props.content,
        isEditing: true
      };

      emit('update:content', newContent);

      nextTick(async () => {
        if (contentElement.value) {
          contentElement.value.focus();
          await nextTick();
          if (!props.isSelected) {
            emit('select', props.content);
          }
        }
      });
      return;
    }

    // 检查是否是复制模式
    const isCopyMode = window._isCopyMode || false;
    console.log('TextElement handleDoubleClick - 复制模式检查:', {
      isCopyMode,
      windowCopyMode: window._isCopyMode,
      contentId: props.content.contentId,
      contentType: props.content.type
    });

    // 如果是复制模式，不清空任何内容，只设置编辑状态
    if (isCopyMode) {
      console.log('复制模式下，保持内容不变');
      const newContent = {
        ...props.content,
        isEditing: true
      };

      emit('update:content', newContent);

      nextTick(async () => {
        if (contentElement.value) {
          contentElement.value.focus();
          await nextTick();
          if (!props.isSelected) {
            emit('select', props.content);
          }
        }
      });
      return;
    }

    // 判断是否是默认文本
    const isDefaultText = isDefaultContent();

    // 如果是默认文本，清空内容
    if (isDefaultText) {
      // 标记已经获得过焦点
      hasFirstFocus.value = true;
      hasEditedContent.value = true;

      const newContent = {
        ...props.content,
        editContent: '',
        content: '',
        isEditing: true,
        userEdited: true
      };

      emit('update:content', newContent);

      nextTick(async () => {
        if (contentElement.value) {
          contentElement.value.innerHTML = '';
          contentElement.value.focus();

          const range = document.createRange();
          const selection = window.getSelection();

          range.setStart(contentElement.value, 0);
          range.collapse(true);

          selection.removeAllRanges();
          selection.addRange(range);
        }
      });
    } else {
      // 保留已编辑内容，只更新编辑状态
      const newContent = { 
        ...props.content,
        isEditing: true
      };

      if (props.content.content && props.content.content !== getDefaultText()) {
        hasEditedContent.value = true;
      }

      emit('update:content', newContent);

      nextTick(async () => {
        if (contentElement.value) {
          contentElement.value.focus();
          await nextTick();
          if (!props.isSelected) {
            emit('select', props.content);
          }
        }
      });
    }
  } else {
    // 非首次点击，保持内容不变，只设置编辑状态
    isEditing.value = true;

    contentElement.value?.focus();

    nextTick(async () => {
      if (!props.isSelected) {
        emit('select', props.content);
      }
    });
  }
};

// 处理失去焦点
const handleBlur = async (event) => {
  console.log('文本元素失去焦点');
  if (!props.editable) return;
  
  try {
    // 检查event是否存在，如果不存在则使用contentElement作为备用
    const targetElement = event?.target || contentElement.value;
    if (!targetElement) {
      console.error('handleBlur: 无法获取目标元素');
      isEditing.value = false;
      return;
    }
    
    // 检查字符限制
    const plainText = targetElement.textContent || '';
    const contentLength = plainText.replace(/{#param\d+#}/g, '参数').length;
    const chineseMatches = plainText.replace(/{#param\d+#}/g, '参数').match(/[\u4e00-\u9fa5]/g);
    const chineseCharCount = chineseMatches ? chineseMatches.length : 0;

    // 使用 Element Plus 的消息提示
    const showMessage = (message) => {
      if (typeof ElMessage !== 'undefined') {
        ElMessage({
          message: message,
          type: 'warning',
          customClass: 'custom-message',
          duration: 5000
        });
      } else if (window.ElMessage) {
        window.ElMessage({
          message: message,
          type: 'warning',
          customClass: 'custom-message',
          duration: 5000
        });
      } else {
        console.warn(message);
      }
    };

    // 各种内容长度限制检查
    if (props.content.isTextTitle === 1) {
      const chineseCharLength = chineseCharCount * 2;
      const englishCharLength = contentLength - chineseCharCount;
      const totalLength = chineseCharLength + englishCharLength;
      if (totalLength > 17 * 2) {
        const overflowChars = totalLength - 17 * 2;
        showMessage(`标题内容超出oppo限制(最多17个字符，中文占两个字符)，当前超出了${Math.ceil(overflowChars / 2)}个字符，超出将会截断，请您修改。`);
      }
    } else if (props.content.isTextTitle === 0) {
      const enterpriseNameLength = 8;
      const chineseCharLength = chineseCharCount * 2;
      const englishCharLength = contentLength - chineseCharCount;
      const totalLength = chineseCharLength + englishCharLength + enterpriseNameLength;
      if (totalLength > 138 * 2) {
        const overflowChars = totalLength - 138 * 2;
        showMessage(`最多支持输入138个字符(【国泰海通】占8个字符)，中文占两个字符，当前超出了${Math.ceil(overflowChars / 2)}个字符！`);
      }
    }

    // 检查内容是否为空
    const hasParams = targetElement.querySelectorAll && targetElement.querySelectorAll('input.j-btn, span.j-btn').length > 0;
    const hasText = targetElement.textContent ? targetElement.textContent.trim().length > 0 : false;
    
    // 额外检查HTML内容中是否包含参数
    const htmlContent = targetElement.innerHTML || '';
    const hasParamInHtml = htmlContent.includes('param-input') || htmlContent.includes('data-param-id');
    
    // 更准确的参数检测：检查是否包含参数相关的HTML标签
    const hasParamElements = htmlContent.includes('<input') && htmlContent.includes('j-btn');
    
    // 检查textContent中是否包含参数标记（去除参数按钮的文本内容）
    const textContentWithoutParams = (targetElement.textContent || '').replace(/{#param\d+#}/g, '').trim();
    const hasRealText = textContentWithoutParams.length > 0;
    
    console.log('handleBlur - 内容检查:', {
      hasParams,
      hasText,
      hasParamInHtml,
      hasParamElements,
      hasRealText,
      textContentWithoutParams,
      htmlContent: htmlContent.substring(0, 200) + '...'
    });
    
    // 判断是否是通知类模板的参数输入框
    const isNotificationParam = props.content.content && 
      typeof props.content.content === 'string' &&
      /^\{#param\d+#\}$/.test(props.content.content.trim());
    
    console.log('handleBlur - 是否是参数输入框:', isNotificationParam, '内容:', props.content.content);
    
    // 如果是参数输入框，不重置内容，保持参数格式
    if (isNotificationParam) {
      console.log('这是参数输入框，保持参数格式不变');
      
      await nextTick();
      const newContent = { 
        ...props.content,
        isEditing: false
      };
      
      isEditing.value = false;
      emit('update:content', newContent);
      emit('blur', props.content);
      return;
    }
    
    // 修复判断逻辑：如果有参数元素或有实际文本内容，就不恢复默认文本
    if (!hasRealText && !hasParams && !hasParamInHtml && !hasParamElements) {
      console.log('handleBlur - 内容为空且无参数，恢复默认文本');
      
      // 优先使用原始接口返回的内容(如果有)，如果没有才使用默认文本
      const originalContent = props.content.originalContent || getDefaultText();
      const defaultText = originalContent !== getDefaultText() ? originalContent : getDefaultText();
      
      await nextTick();
      const newContent = { 
        ...props.content,
        editContent: undefined,
        isEditing: false,
        // 恢复原始内容或默认文本
        content: defaultText,
        userEdited: false // 重置为未编辑状态
      };
      
      isEditing.value = false;
      isFirstClick.value = true;
      // 重置首次焦点状态，这样下次焦点时会再次清空默认文本
      hasFirstFocus.value = false;
      hasEditedContent.value = false;
      
      emit('update:content', newContent);
      
      // 确保内容更新后，DOM也被更新
      await nextTick();
      if (contentElement.value) {
        contentElement.value.innerHTML = defaultText;
      }
    } else {
      console.log('handleBlur - 有内容或参数，保存当前状态');
      
      // 确保参数在HTML中正确格式化
      const cleanedHtml = targetElement.innerHTML || '';
      
      await nextTick();
      // 有内容，保存当前内容到正式内容
      const newContent = { 
        ...props.content,
        content: cleanedHtml,
        editContent: cleanedHtml,
        isEditing: false,
        userEdited: true // 标记为用户已编辑
      };
      
      isEditing.value = false;
      hasEditedContent.value = true;
      
      emit('update:content', newContent);
    }
    
    emit('blur', props.content);
  } catch (error) {
    console.error('处理失焦事件出错:', error);
    // 即使出错也要确保编辑状态被重置
    isEditing.value = false;
  }
};

// 处理输入法开始事件
const handleCompositionStart = () => {
  isComposing.value = true;
};

// 处理输入法结束事件
const handleCompositionEnd = (event) => {
  isComposing.value = false;
  
  // 输入法编辑结束后，手动触发内容更新
  setTimeout(() => {
    if (contentElement.value) {
      handleContentEditableInput({
        target: contentElement.value
      });
    }
  }, 0);
};

// 将光标移到末尾
const moveCaretToEnd = (element) => {
  try {
    const range = document.createRange();
    const selection = window.getSelection();
    
    // 使用标准的光标定位逻辑
    range.selectNodeContents(element);
    range.collapse(false); // false表示移到结尾
    
    selection.removeAllRanges();
    selection.addRange(range);
  } catch (error) {
    console.error('移动光标到末尾失败:', error);
  }
};

// 暴露方法给父组件调用
defineExpose({
  handleParamInsert,
  focusElement: () => {
    if (contentElement.value) {
      contentElement.value.focus();
    }
  }
});

// 组件挂载时，初始化内容
onMounted(() => {
  
  // 保存原始内容，用于后续恢复
  if (props.content && props.content.content && !props.content.originalContent) {
    const contentWithOriginal = {
      ...props.content,
      originalContent: props.content.content
    };
    
    emit('update:content', contentWithOriginal);
  }
  
  // 如果内容中有参数，格式化显示
  if (props.content.content) {
    // 先同步设置内容，避免闪烁
    if (contentElement.value) {
      // 在非编辑模式下（如卡片模式），优先显示实际内容
      let displayContent = props.content.content;
      
      // 只有在编辑模式下且内容为空时才使用默认文本
      if (props.editable && (!displayContent || displayContent.trim() === '')) {
        displayContent = getDefaultText();
      }
      
      
      // 在卡片模式下（usage为'list'），直接显示用户内容，不进行参数格式化
      if (props.usage === 'list' && displayContent) {
        contentElement.value.innerHTML = displayContent;
      } else {
        // 编辑模式下同步设置内容，避免使用setContent导致的延迟
        contentElement.value.innerHTML = displayContent;
      }
    }
    
    // 然后在nextTick中进行进一步的格式化处理（仅在编辑模式下）
    nextTick(() => {
      try {
        // 只有在编辑模式下才进行参数格式化，卡片模式下不需要
        if (props.usage !== 'list' && props.editable) {
          let displayContent = props.content.content;
          if (!displayContent || displayContent.trim() === '') {
            displayContent = getDefaultText();
          }
          console.log('TextElement: 编辑模式，进行参数格式化', displayContent);
          setContent(displayContent);
        }
      } catch (error) {
        console.error('设置内容时出错:', error);
        // 如果设置内容失败，直接设置innerHTML
        if (contentElement.value) {
          contentElement.value.innerHTML = props.content.content || '';
        }
      }
    });
  }
  
  // 添加参数事件监听
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.on('insert-param', (paramData) => {
      
      // 修复条件：如果组件可编辑且（被选中或正在编辑），就处理参数插入
      if (props.editable && (props.isSelected || isEditing.value)) {
        console.log('TextElement: 开始处理参数插入');
        handleParamInsert(paramData);
      } else {
        console.log('TextElement: 跳过参数插入，条件不满足');
      }
    });
  } else {
    console.warn('TextElement: PARAM_EVENT_BUS 不存在');
  }
});

// 在组件卸载前移除事件监听和全局引用
onBeforeUnmount(() => {
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.off('insert-param');
  }
  
  // 移除全局引用
  if (window.TEXT_ELEMENT_REFS) {
    const index = window.TEXT_ELEMENT_REFS.findIndex(item => 
      item.ref === contentElement);
    if (index !== -1) {
      window.TEXT_ELEMENT_REFS.splice(index, 1);
    }
  }
});

// 监听选中状态
watch(() => props.isSelected, (selected) => {
  // 当组件被选中时，只有在用户主动点击内容时才触发焦点事件
  if (selected && !isEditing.value) {
    // 统一处理：所有模板类型都使用相同的自动获得焦点逻辑
    // 检查是否是通过点击空白区域触发的选中（这种情况下不应该自动获得焦点）
    if (window._isBlankAreaClick) {
      // 如果是点击空白区域触发的，不自动获得焦点
      console.log('TextElement: 检测到空白区域点击标记，跳过自动焦点');
      window._isBlankAreaClick = false;
      return;
    }
    
    // 检查是否是通过clearSelectedContent触发的（这种情况下也不应该自动获得焦点）
    if (window._isClearingSelection) {
      console.log('TextElement: 检测到清除选择标记，跳过自动焦点');
      window._isClearingSelection = false;
      return;
    }
    
    // 红包模板的特殊处理：只有在用户主动点击文本内容时才自动获得焦点
    if (window.TEMPLATE_IS_REDPACKET) {
      // 检查是否是用户主动的交互行为
      if (!window._userInteractionFlag) {
        console.log('TextElement: 红包模板被选中，但非用户主动交互，不自动获得焦点');
        return;
      }
      console.log('TextElement: 红包模板被选中，检测到用户主动交互，允许自动获得焦点');
    }
    
    // 防止重复聚焦
    if (window._lastSelectTimestamp && (Date.now() - window._lastSelectTimestamp < 100)) {
      return;
    }
    window._lastSelectTimestamp = Date.now();

    // 设置延时以确保DOM已更新
    nextTick(() => {
      if (contentElement.value) {
        contentElement.value.focus();
        // 保持光标位置不变
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          if (contentElement.value.contains(range.commonAncestorContainer)) {
            return;
          }
        }
        // 如果没有有效选区，将光标移到末尾
        moveCaretToEnd(contentElement.value);
        // 确保文本设置面板显示
        emit('select', props.content);
      }
    });
  }

  // 当组件被取消选中时，重置首次点击状态和编辑状态
  if (!selected) {
    isFirstClick.value = true;
    isEditing.value = false;
  }
});

// 监听内容变化
watch(() => props.content, (newContent, oldContent) => {
  if (!newContent || !contentElement.value) return;
  
  // 检查内容是否真的发生了变化
  const oldContentText = oldContent?.content || oldContent?.editContent || '';
  const newContentText = newContent.content || newContent.editContent || '';
  
  if (oldContentText !== newContentText) {
    
    // 如果不在编辑状态，更新显示内容
    if (!isEditing.value) {
      nextTick(() => {
        try {
          // 在非编辑模式下（如卡片模式），优先显示实际内容
          let displayContent = newContentText;
          
          // 只有在编辑模式下且内容为空时才使用默认文本
          if (props.editable && (!displayContent || displayContent.trim() === '')) {
            displayContent = getDefaultText();
          }
          // 在卡片模式下（usage为'list'），直接显示用户内容，不进行参数格式化
          if (props.usage === 'list' && displayContent) {
            console.log('TextElement watch: 卡片模式（usage=list），直接显示用户内容', displayContent);
            if (contentElement.value) {
              contentElement.value.innerHTML = displayContent;
              console.log('TextElement watch: 卡片模式内容已设置，innerHTML=', contentElement.value.innerHTML);
            }
          } else {
            // 编辑模式下才进行参数格式化
            console.log('TextElement watch: 编辑模式，进行参数格式化', displayContent);
            setContent(displayContent);
          }
          
          // 如果有编辑内容，标记为已编辑
          if (newContent.editContent && newContent.editContent !== getDefaultText()) {
            hasEditedContent.value = true;
          }
        } catch (error) {
          console.error('更新文本内容显示时出错:', error);
          // 如果设置内容失败，直接设置innerHTML
          if (contentElement.value) {
            // 在非编辑模式下优先显示实际内容
            let displayContent = newContentText;
            if (props.editable && (!displayContent || displayContent.trim() === '')) {
              displayContent = getDefaultText();
            }
            contentElement.value.innerHTML = displayContent;
          }
        }
      });
    } else if (isEditing.value && newContentText !== contentElement.value.innerHTML) {
      // 如果在编辑状态且内容不同，说明是外部更新，需要同步
      console.log('TextElement watch: 编辑状态下同步外部内容变化');
      nextTick(() => {
        if (contentElement.value && contentElement.value.innerHTML !== newContentText) {
          setContent(newContentText);
        }
      });
    }
  }
}, { deep: true });

// 监听usage属性变化，处理编辑模式和卡片模式的切换
watch(() => props.usage, (newUsage, oldUsage) => {
  
  // 当从编辑模式切换到卡片模式时，重新设置内容显示
  if (oldUsage === 'editor' && newUsage === 'list' && contentElement.value) {
    const content = props.content?.content || props.content?.editContent || '';
    
    if (content) {
      // 同步设置内容，避免闪烁
      contentElement.value.innerHTML = content;
    }
  }
  
  // 当从undefined或null切换到list模式时（组件初始化），确保内容正确显示
  if (!oldUsage && newUsage === 'list' && contentElement.value) {
    const content = props.content?.content || props.content?.editContent || '';
    if (content) {
      contentElement.value.innerHTML = content;
    }
  }
});
</script>

<style scoped lang="scss">
.preview-title, .preview-desc, .preview-name {
  margin: 0 16px;
  outline: none;
  word-break: break-all;
  color: #111;
  height: 24px;
  display: block;
  box-sizing: border-box;
  overflow: auto;
}

.preview-title {
  font-size: 16px;
  font-weight: bold;
  margin: 8px 16px;
}

.preview-desc {
  font-size: 13px;
  line-height: 1.6;
  position: relative;
  text-indent: 76px; 
  margin-top: 0;
  height: 64px;
  overflow: auto;
}

.preview-desc:before {
  content: "【国泰海通】";
  color: #faad14;
  font-weight: normal;
  position: absolute;
  top: 0;
  left: 0;
  text-indent: 0;
}

.preview-name {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.selected {
  outline: 1px dashed #409eff;
}

.editable {
  cursor: text;
}

:deep(input.j-btn), :deep(span.j-btn) {
  border: none !important;
  border-radius: 0 !important;
  color: #dcbb84;
  background: none !important;
  margin: 0 3px;
  font-size: inherit;
  line-height: inherit;
  cursor: default;
  padding: 0;
  display: inline-block;
  vertical-align: baseline;
  height: auto;
  min-height: auto;
  box-shadow: none !important;
  font-weight: 700;
}

:deep(input.j-btn:hover), 
:deep(input.j-btn:focus),
:deep(span.j-btn:hover),
:deep(span.j-btn:focus) {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  background: none !important;
}

/* 红包模板描述文本特殊样式 */
.red-packet-desc {
  color: #fff;
  text-align: center;
  margin: 0 10px;
  text-indent: 0 !important; /* 统一文本缩进处理 */
  width: 90%;
  overflow: auto;
  font-weight: bold;
  position: relative;
}

/* 统一企业签名显示逻辑：与普通模板保持一致 */
.red-packet-desc:before {
  content: "【国泰海通】";
  color: #faad14;
  font-weight: normal;
  position: absolute;
  top: 0;
  left: 0;
  text-indent: 0;
}
</style> 