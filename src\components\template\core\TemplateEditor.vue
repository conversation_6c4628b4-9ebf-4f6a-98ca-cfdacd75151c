﻿<template>
  <div class="template-editor-container">
    <el-dialog class="dialog-box" v-model="dialogVisible" fullscreen :show-close="false" style="padding: 50px 0 0 200px">
      <!-- 头部区域 -->
      <template #header>
        <TemplateHeader 
          :template-name="templateName" 
          @update:template-name="templateName = $event"
          @save="saveTemplate"
          @submit="submitTemplate"
          @close="handleClose"
        />
      </template>

      <section class="dialog-content" @click="handleDialogContentClick">
        <!-- 中间预览区域 -->
        <div class="middle-section">
          <template v-if="selectedTemplate">
            <TemplatePreviewContainer
              :contents="selectedTemplateContents"
              :selected-content="selectedContent"
              :template-type="getTemplateType()"
              :editable="true"
              :get-media-url="getMediaUrl"
              :ecommerce-display-data="isEcommerceTemplate ? getEcommerceDisplayData() : null"
              :multi-product-display-data="isMultiProductTemplate ? getMultiProductDisplayData() : null"
              :coupon-product-display-data="isCouponProductTemplate ? couponProductDisplayData : null"
              @select-content="selectContent"
              @update:content="updateContent"
            />
          </template>
          <div v-else class="empty-template-message">
            请选择左侧版式类型创建模板
          </div>
        </div>

        <!-- 左侧版式 -->
        <TemplateSidebar
          :templates="templates"
          :active-tab="activeTab"
          :selected-template="selectedTemplate"
          :get-media-url="getMediaUrl"
          :app-key="appKey"
          @tab-change="handleTabChange"
          @template-click="handleTemplateClick"
        />

        <!-- 右侧设置区域 -->
        <TemplateSettings
          v-if="selectedContent && !isNotificationTemplate && !isMultiTextTemplate && !isLongTextTemplate && !isEcommerceTemplate && !isMultiProductTemplate && !isCardVoucherTemplate && !isCouponProductTemplate"
          :content="selectedContent"
          :template="selectedTemplate"
          :app-key="appKey"
          @update:content="handleContentSettingsUpdate"
          @input="handleContentSettingsUpdate"
          @param-insert="handleParamInsert"
          @param-manage="handleParamManage"
          @update-card-image="handleUpdateCardImage"
          @update-card-content="handleUpdateCardContent"
          @add-card="handleAddCard"
          @remove-card="handleRemoveCard"
          @settings-change="handleHorizontalSwipeSettingsChange"
          @card-selected="handleCardSelected"
          @close="closeRightPanel"
        />
        
        <!-- 通知类模板的设置面板 -->
        <TemplateSettings
          v-if="isNotificationTemplate && (selectedContent || showNotificationSettings)"
          :content="selectedContent || notificationSettingsContent"
          :template="selectedTemplate"
          :app-key="appKey"
          @update:content="handleContentSettingsUpdate"
          @input="handleContentSettingsUpdate"
          @param-insert="handleParamInsert"
          @param-manage="handleParamManage"
          @param-count-change="handleParamCountChange"
          @button-count-change="handleButtonCountChange"
          @close="closeRightPanel"
        />
        
        <!-- 多图文模板的设置面板 -->
        <TemplateSettings
          v-if="isMultiTextTemplate && (selectedContent || showMultiTextSettings)"
          :content="selectedContent || multiTextSettingsContent"
          :template="selectedTemplate"
          :app-key="appKey"
          @update:content="handleContentSettingsUpdate"
          @input="handleContentSettingsUpdate"
          @param-insert="handleParamInsert"
          @param-manage="handleParamManage"
          @pair-count-change="handlePairCountChange"
          @close="closeRightPanel"
        />
        
        <!-- 长文本模板的设置面板 -->
        <TemplateSettings
          v-if="isLongTextTemplate && (selectedContent || showLongTextSettings)"
          :content="selectedContent || longTextSettingsContent"
          :template="selectedTemplate"
          :app-key="appKey"
          @update:content="handleContentSettingsUpdate"
          @input="handleContentSettingsUpdate"
          @param-insert="handleParamInsert"
          @param-manage="handleParamManage"
          @settings-change="handleLongTextSettingsChange"
          @close="closeRightPanel"
        />
        
        <!-- 电商模板的设置面板 -->
        <TemplateSettings
          v-if="isEcommerceTemplate && (selectedContent || showEcommerceSettings)"
          :content="selectedContent || ecommerceSettingsContent"
          :template="selectedTemplate"
          :app-key="appKey"
          @update:content="handleContentSettingsUpdate"
          @input="handleContentSettingsUpdate"
          @param-insert="handleParamInsert"
          @param-manage="handleParamManage"
          @close="closeRightPanel"
        />
        
        <!-- 多商品模板的设置面板 -->
        <TemplateSettings
          v-if="isMultiProductTemplate && (selectedContent || showMultiProductSettings)"
          :content="selectedContent || multiProductSettingsContent"
          :template="selectedTemplate"
          :app-key="appKey"
          @update:content="handleContentSettingsUpdate"
          @input="handleContentSettingsUpdate"
          @param-insert="handleParamInsert"
          @param-manage="handleParamManage"
          @settings-change="handleMultiProductSettingsChange"
          @close="closeRightPanel"
        />
        
        <!-- 单卡券模板的设置面板 -->
        <TemplateSettings
          v-if="isCardVoucherTemplate && (selectedContent || showCardVoucherSettings)"
          :content="selectedContent || cardVoucherSettingsContent"
          :template="selectedTemplate"
          :app-key="appKey"
          @update:content="handleContentSettingsUpdate"
          @input="handleContentSettingsUpdate"
          @param-insert="handleParamInsert"
          @param-manage="handleParamManage"
          @contents-change="handleCardVoucherSettingsChange"
          @close="closeRightPanel"
        />
        
        <!-- 券+商品模板的设置面板 -->
        <TemplateSettings
          v-if="isCouponProductTemplate && (selectedContent || showCouponProductSettings)"
          :content="selectedContent || couponProductSettingsContent"
          :template="selectedTemplate"
          :app-key="appKey"
          @update:content="handleContentSettingsUpdate"
          @input="handleContentSettingsUpdate"
          @param-insert="handleParamInsert"
          @param-manage="handleParamManage"
          @settings-change="handleCouponProductSettingsChange"
          @close="closeRightPanel"
        />
      </section>

      <!-- 参数管理对话框 -->
      <ParamManageDialog 
        v-model:visible="paramManageVisible" 
        :content="selectedContent"
        :all-params="templateParams"
        @confirm="handleParamManageConfirm"
      />
      
      <!-- 切换模板的对话框 -->
      <TemplateSwitchDialog
        :model-value="switchTemplateDialogVisible"
        @update:model-value="switchTemplateDialogVisible = $event"
        @confirm="confirmSwitchTemplate"
        @cancel="cancelSwitchTemplate"
      />
      
      <!-- 继续编辑的对话框 -->
      <ContinueEditDialog
        :model-value="continueEditDialogVisible"
        @update:model-value="continueEditDialogVisible = $event"
        @continue="continueEditing"
        @restart="restartEditing"
      />

      <!-- 关闭确认对话框 -->
      <CloseConfirmDialog
        :model-value="closeConfirmDialogVisible"
        @update:model-value="closeConfirmDialogVisible = $event"
        @confirm="confirmClose"
        @cancel="cancelClose"
      />

      <!-- 提交预览的对话框 -->
      <PreviewSubmitDialog
        :model-value="previewSubmitDialogVisible"
        @update:model-value="previewSubmitDialogVisible = $event"
        :template-data="{
          templateName: templateName,
          contents: selectedTemplateContents,
          scene: copyTemplateOriginalData?.scene || '',
          useId: copyTemplateOriginalData?.useId || '1',
          smsExample: copyTemplateOriginalData?.smsExample || '',
          aimSmsSigns: copyTemplateOriginalData?.aimSmsSigns || [],
          factoryInfos: copyTemplateOriginalData?.factoryInfos || null
        }"
        @confirm="onConfirmSubmit"
        @cancel="cancelSubmitTemplate"
        @update:template-name="handlePreviewTemplateNameUpdate"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import api from "@/api/index";
import cloneDeep from 'lodash/cloneDeep';
import * as paramUtils from '@/utils/paramUtils';
import { dispatchTemplateReset, dispatchTemplateSwitchEvent, switchTemplate } from '@/utils/templateEvents';
import templateFactory from '@/factories/TemplateFactory.js';
import MultiProductTemplate from '@/components/template/types/MultiProductTemplate.js';
import EcommerceTemplate from '@/components/template/types/EcommerceTemplate.js';
import NotificationTemplate from '@/components/template/types/NotificationTemplate.js';
import MultiTextTemplate from '@/components/template/types/MultiTextTemplate.js';
import { getMediaUrl as getMediaUrlFromUtils } from '@/utils/mediaUtils';
import getTemBasicList1 from '@/api/getTemBasicList1';
import { 
  CLICK_EVENT_TYPES, 
  ClickEventValidator, 
  ClickEventTypeConverter,
  ActionJsonGenerator,
   validateClickEvent, extractClickEventValidationFields,
  globalClickEventCache 
} from '@/utils/clickEventManager';


// 提取解析 pages 字段的逻辑
const parsePages = (pages) => {
  if (typeof pages === 'string') {
    try {
      return JSON.parse(pages);
    } catch (e) {
      console.error('解析pages字段失败:', e);
      return [];
    }
  }
  return pages;
};

const props = defineProps({
  appKey: {
    type: String,
    required: true
  },
  dirId: {
    type: String,
    required: true
  },
  subType: {
    type: String,
    default: ''
  },
  templatesData: { 
    type: Array,
    default: () => []
  }
});

const emit = defineEmits([
  'save', 
  'submit',
  'update:content',
  'input',
  'param-insert',
  'param-manage',
  'settings-change',
  'close',
  'param-count-change',
  'button-count-change',
  'pair-count-change',
  'contents-change',
  'update-sub-type'
]);

let onSave = null;

// 核心状态
const dialogVisible = ref(false);
const templates = ref([]);
const selectedTemplate = ref(null);
const selectedTemplateContents = ref([]);
const selectedContent = ref(null);
const templateName = ref('');
const activeTab = ref('');

// 编辑状态管理
const hasEditedContent = ref(false);
const hasChanges = ref(false);

// 对话框状态
const paramManageVisible = ref(false);
const switchTemplateDialogVisible = ref(false);
const continueEditDialogVisible = ref(false);
const closeConfirmDialogVisible = ref(false);
const previewSubmitDialogVisible = ref(false);

// 模板参数
const templateParams = ref([]);
const pendingTemplateChange = ref(null);

const templateDirId = ref(''); // 保存模板详情中的原始dirId
const copyTemplateOriginalData = ref(null); // 存储复制模板的原始数据

// 通知设置
const notificationSettings = reactive({
  maxVisibleParams: 2,
  maxVisibleButtons: 2
});

// 多图文设置
const multiTextSettings = reactive({
  pairCount: 1
});

// 长文本设置
const longTextSettings = reactive({
  selectedStyle: 'simple',
});
// 横滑设置面板显示状态
const showHorizontalSwipeSettings = ref(false);

// 横滑设置内容
const horizontalSwipeSettingsContent = ref({
  type: 'horizontalSwipeSettings',
  contentId: 'horizontalswipe-settings',
  content: '横滑设置',
  positionNumber: 0,
  isTextTitle: false,
  aimCurrencyDisplay: false,
  isHorizontalSwipeSettings: true
});

// 监听通知设置变化，触发模板重新渲染
watch(() => [notificationSettings.maxVisibleParams, notificationSettings.maxVisibleButtons], 
  ([newParams, newButtons], [oldParams, oldButtons]) => {
    // 如果当前选中的是通知类模板，触发重新渲染
    if (selectedTemplate.value && selectedTemplate.value.tplType === '通知类') {
      
      // 通过事件总线通知所有相关组件
      if (window.PARAM_EVENT_BUS) {
        window.PARAM_EVENT_BUS.emit('notification-settings-changed', {
          maxVisibleParams: newParams,
          maxVisibleButtons: newButtons
        });
      }
      
      // 强制更新模板内容显示
      nextTick(() => {
        // 触发模板内容的重新计算
        const event = new CustomEvent('template-settings-updated', {
          detail: {
            maxVisibleParams: newParams,
            maxVisibleButtons: newButtons
          }
        });
        document.dispatchEvent(event);
      });
    }
  }, 
  { deep: true }
);

// 监听多图文设置变化，触发模板重新渲染
watch(() => multiTextSettings.pairCount, 
  (newPairCount, oldPairCount) => {   
    // 如果当前选中的是多图文模板，触发重新渲染
    if (selectedTemplate.value && (selectedTemplate.value.templateType === 'multitext' || 
        selectedTemplate.value.templateName?.includes('多图文') ||
        selectedTemplate.value.tplType === '多图文')) {

      // 通过事件总线通知所有相关组件
      if (window.PARAM_EVENT_BUS) {
        window.PARAM_EVENT_BUS.emit('multitext-settings-changed', {
          pairCount: newPairCount
        });
      }
      
      // 强制更新模板内容显示
      nextTick(() => {
        // 触发模板内容的重新计算
        const event = new CustomEvent('multitext-settings-updated', {
          detail: {
            pairCount: newPairCount
          }
        });
        document.dispatchEvent(event);
      });
    }
  }
);

provide('currentTemplate', selectedTemplate); // 提供顶层模板引
provide('selectedTemplateContents', selectedTemplateContents); // 提供模板内容数据
provide('notificationSettings', notificationSettings); // 提供通知设置
provide('multiTextSettings', multiTextSettings); // 提供多图文设置
provide('longTextSettings', longTextSettings); // 提供长文本设置
provide('horizontalSwipeSettings', horizontalSwipeSettingsContent); // 提供横滑设置

// 设置组件的provide，提供预览模式标识
provide('isPreviewMode', true); // 在模板编辑器中，始终为预览模式

// 监听内容变化，强制重新渲染轮播图
watch(() => props.content, (newContent) => {
  
  // 当轮播图数据变化时，强制重新渲染轮播图组件
  if (newContent && newContent.carouselImages) {
    nextTick(() => {
      if (carouselRef.value) {
        carouselRef.value.$forceUpdate && carouselRef.value.$forceUpdate();
      }
    });
  }
}, { deep: true, immediate: true }); // 启用深度监听

// 获取媒体URL
const getMediaUrl = (src) => {
  // 使用媒体工具函数处理URL
  return getMediaUrlFromUtils(src);
};

// 监听模板变化，初始化内容
watch(() => selectedTemplate.value, async (newTemplate) => {
  if (!newTemplate) {
    selectedTemplateContents.value = [];
    selectedContent.value = null;
    
    // 重置所有模板设置状态
    ecommerceSettingsState.value = null;
    multiProductSettingsState.value = null;
    couponProductSettingsState.value = null;
    return;
  }

  try {
    // 重置所有模板设置状态（确保模板切换时状态被重置，避免数据污染）
    ecommerceSettingsState.value = null;
    multiProductSettingsState.value = null;
    couponProductSettingsState.value = null;

    // 重置全局校验失败标志，避免影响其他模板
    window._isEcommerceValidationFailed = false;
    
    // 使用模板工厂获取对应的处理器
    const templateType = templateFactory.detectTemplateType(newTemplate);
    const templateHandler = templateFactory.getTemplateHandler(templateType);
    
    // 增强pages解析：优先使用已解析的数组，否则尝试二次解析
    const rawPages = newTemplate.pages;
    const parsedPages = Array.isArray(rawPages) ? 
                      rawPages : 
                      (typeof rawPages === 'string' ? JSON.parse(rawPages) : []);
    
    // 特别处理红包模板：提取所有页面的contents合并
    if (isRedPacketTemplate(newTemplate)) { 
      const allContents = parsedPages.flatMap(page => page.contents || []);
      selectedTemplateContents.value = cloneDeep(allContents).map(content => ({
        ...content,
        userId: newTemplate.userId,
        // 为红包模板的特殊内容类型添加标识
        isRedPacket: true 
      }));
    } 
    // 特别处理轮播图模板：将单个图片内容转换为轮播图数组格式
    else if (templateFactory.isCarouselTemplate(newTemplate)) {
      console.log('轮播图模板cardId:', newTemplate.cardId);
      
      const allContents = parsedPages.flatMap(page => page.contents || []);
      
      // 找到图片内容
      const imageContent = allContents.find(content => content.type === 'image');
      
      if (imageContent) {
        // 创建干净的轮播图数据结构，确保不被其他模板污染
        const cleanCarouselContent = {
          contentId: imageContent.contentId,
          pageId: imageContent.pageId,
          templateId: imageContent.templateId,
          type: 'image',
          content: imageContent.content,
          src: imageContent.src || imageContent.defaultSrc || '/aim_files/aim_defult/defaultImg2.jpg',
          positionNumber: imageContent.positionNumber,
          isTextTitle: imageContent.isTextTitle,
          aimCurrencyDisplay: imageContent.aimCurrencyDisplay,
          isCarousel: true,
          carouselImages: [
            {
              src: imageContent.src || imageContent.defaultSrc || '/aim_files/aim_defult/defaultImg2.jpg',
              alt: '图片1',
              positionNumber: 1, // 第一张图片的positionNumber应该为1
              clickEvent: {
                type: 'browser', // 使用clickEventManager中定义的映射类型
                url: '',
                phone: '',
                text: '',
                app: '',
                packageName: '',
                floorType: '0',
                quick: '',
                email: '',
                emailSubject: '',
                emailBody: '',
                schedule: '',
                scheduleStartTimeString: '',
                scheduleEndTimeString: '',
                popup: '',
                popupContent: '',
                popupButtonText: ''
              }
            }
          ],
          currentImageIndex: 0
        };
        
        // 替换原来的图片内容，确保其他内容项也是干净的
        const updatedContents = allContents.map(content => {
          if (content.type === 'image') {
            return cleanCarouselContent;
          } else {
            // 确保其他内容项也是干净的，移除可能的污染字段
            const cleanContent = {
              contentId: content.contentId,
              pageId: content.pageId,
              templateId: content.templateId,
              type: content.type,
              content: content.content,
              positionNumber: content.positionNumber,
              isTextTitle: content.isTextTitle,
              aimCurrencyDisplay: content.aimCurrencyDisplay
            };
            
            // 如果有src字段，保留它
            if (content.src) {
              cleanContent.src = content.src;
            }
            
            return cleanContent;
          }
        });
        
        selectedTemplateContents.value = cloneDeep(updatedContents).map(content => ({
          ...content,
          userId: newTemplate.userId
        }));
        
      } else {
        // 如果没有找到图片内容，使用默认处理
        const initialContents = templateHandler.initializeContents(newTemplate);
        selectedTemplateContents.value = cloneDeep(initialContents).map(content => ({
          ...content,
          userId: newTemplate.userId
        }));
      }
    }
    else {
      // 初始化模板内容
      let initialContents;
      if (templateType === 'longtext') {
        // 长文本模板需要传入settings参数
        initialContents = templateHandler.initializeContents(newTemplate, longTextSettings);
      } else {
        initialContents = templateHandler.initializeContents(newTemplate);
      }
      
      // 如果是多图文模板且没有内容，创建默认内容
      if (templateType === 'multitext' && (!initialContents || initialContents.length === 0)) {
        initialContents = templateHandler.createDefaultContents(newTemplate);
      }
      
      // 对于多图文模板，确保内容有正确的role属性
      if (templateType === 'multitext' && initialContents && initialContents.length > 0) {
        // 检查是否有内容缺少role属性
        const hasRoleIssue = initialContents.some(content => !content.role);
        if (hasRoleIssue) {
          // 重新调用initializeContents确保role属性正确
          initialContents = templateHandler.initializeContents(newTemplate);
        }
      }
      
      selectedTemplateContents.value = cloneDeep(initialContents).map(content => ({
        ...content,
        userId: newTemplate.userId // 从顶层模板数据注入userId
      }));
      
    }

    selectedContent.value = null; // 确保选中版式后不立即显示右侧面板
    
    // 如果是多图文模板，自动显示多图文设置面板
    if (templateType === 'multitext') {
      showMultiTextSettings.value = true;
      showNotificationSettings.value = false;
      showLongTextSettings.value = false;
      // 删除横滑设置
      showSettings.value = false;
      selectedContent.value = multiTextSettingsContent.value;
    }
    // 如果是长文本模板，自动显示长文本设置面板
    else if (templateType === 'longtext') {
      
      // 重置长文本设置到默认状态
      Object.assign(longTextSettings, {
        selectedStyle: 'simple',
      });
      
      // 通过事件总线通知所有相关组件重置
      if (window.PARAM_EVENT_BUS) {
        window.PARAM_EVENT_BUS.emit('longtext-settings-reset', {
          selectedStyle: 'simple'
        });
      }
      
      // 强制更新模板内容显示
      nextTick(() => {
        // 触发模板内容的重新计算
        const event = new CustomEvent('longtext-settings-updated', {
          detail: { selectedStyle: 'simple' }
        });
        document.dispatchEvent(event);
      });
      
      showLongTextSettings.value = true;
      showNotificationSettings.value = false;
      showMultiTextSettings.value = false;
      // 删除横滑设置
      showSettings.value = false;
      selectedContent.value = longTextSettingsContent.value;
    }
    // 删除横滑模板的特殊处理，让它使用标准内容设置
    
    // 如果是电商模板，初始化电商设置状态
    if (templateType === 'ecommerce') {
      
      // 从模板API数据中提取电商数据
      const extractedData = EcommerceTemplate.extractEcommerceDataFromContents(selectedTemplateContents.value)
      
      // 使用提取的数据初始化电商设置状态
      ecommerceSettingsState.value = EcommerceTemplate.initializeEcommerceSettingsState(extractedData);
    }
  } catch (error) {
    console.error('初始化模板内容时出错:', error);
    selectedTemplateContents.value = [];
    selectedContent.value = null;
  }
});

// 初始化
onMounted(() => {
  // 初始化全局标记
  window.TEMPLATE_IS_REDPACKET = false;
  window.TEMPLATE_DIALOG_DATA = {};
  
  // 处理模板数据
  templates.value = processTemplatesData(props.templatesData);
  
  if (templates.value.length > 0 && !activeTab.value) {
    activeTab.value = templates.value[0].tplType;
  }
  
  // 添加全局点击事件监听
  document.addEventListener('click', handleDocumentClick);
  
  // 添加长文本确保按钮事件监听
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.on('longtext-ensure-buttons', (data) => {
      console.log('收到长文本确保按钮事件:', data);
      
      if (data.selectedStyle === 'general' && data.requiredButtons === 2) {
        // 获取当前按钮数量
        const currentButtons = selectedTemplateContents.value.filter(content => content.type === 'button');
        
        if (currentButtons.length < 2) {
          
          // 获取模板处理器
          const templateType = templateFactory.detectTemplateType(selectedTemplate.value);
          const templateHandler = templateFactory.getTemplateHandler(templateType);
          
          if (templateHandler && templateHandler.ensureButtonsForGeneralStyle) {
            // 使用模板处理器确保有两个按钮
            const updatedContents = templateHandler.ensureButtonsForGeneralStyle(selectedTemplateContents.value, { selectedStyle: 'general' });
            selectedTemplateContents.value = updatedContents;
          }
        }
      }
    });
  }
});

// 清理事件监听
onBeforeUnmount(() => {
  // 移除全局点击事件监听
  document.removeEventListener('click', handleDocumentClick);
  
  // 清理长文本确保按钮事件监听
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.off('longtext-ensure-buttons');
  }
});

// 处理模板数据
const processTemplatesData = (templatesData) => {
  return templatesData.map(template => {
    // 解析pages数据
    template.pages = parsePages(template.pages);
    
    // 检测模板类型并使用对应的处理器
    const templateType = templateFactory.detectTemplateType(template);
    if (templateType === 'notification') {
      const processor = templateFactory.createProcessor(templateType);
      if (processor && processor.processTemplate) {
        template = processor.processTemplate(template);
      }
    }
    
    // 解析factoryInfos
    if (template.factoryInfos && typeof template.factoryInfos === 'string') {
      template.factoryInfos = template.factoryInfos.split(',').map(item => item.trim());
    } else {
      template.factoryInfos = [];
    }
    
    return template;
  });
};

// 创建全局参数管理器
const createGlobalParamManager = () => {
  
  const usedParamIds = new Set(); // 已使用的参数ID集合
  const deletedParamIds = []; // 已删除但可重用的参数ID
  let isInitialized = false;
  
  // 从DOM收集当前使用的所有参数ID
  const collectParamIdsFromDOM = () => {
    try {
      
      // 确保已经打开了模板编辑器
      if (!dialogVisible.value) {
        return;
      }
      
      // 创建临时Set来收集ID
      const tempIds = new Set();
      
      // 获取模板编辑器的DOM元素
      const templateEditor = document.querySelector('.template-editor-container');
      if (!templateEditor) {
        return;
      }
      
      // 搜索整个文档中的所有可能包含参数的元素
      const allParamElements = templateEditor.querySelectorAll('input.j-btn[data-param-id], span.j-btn[data-param-id], .param-input, [data-param-id]');
      
      // 从所有参数元素中提取ID
      allParamElements.forEach(el => {
        const paramId = parseInt(el.getAttribute('data-param-id'));
        if (!isNaN(paramId)) {
          tempIds.add(paramId);
        }
      });
      
      // 查找所有内容中的参数 (HTML和文本)
      const editableElements = templateEditor.querySelectorAll('[contenteditable="true"], .editable-link-url, .editable-content');
      editableElements.forEach(el => {
        if (!el) return;
        
        // 从HTML内容中提取参数ID
        const content = el.innerHTML || '';
        const regex = /{#param(\d+)#}/g;
        let match;
        while ((match = regex.exec(content)) !== null) {
          const paramId = parseInt(match[1]);
          if (!isNaN(paramId)) {
            tempIds.add(paramId);
          }
        }
      });
      
      // 完成收集后，更新usedParamIds
      usedParamIds.clear();
      tempIds.forEach(id => usedParamIds.add(id));
      
      // 全局广播参数ID变化
      if (window.PARAM_EVENT_BUS) {
        window.PARAM_EVENT_BUS.emit('global-params-updated', Array.from(usedParamIds));
      }
    } catch (error) {
      console.error('收集参数ID时出错:', error);
    }
  };
  
  // 获取下一个可用的参数ID
  const getNextId = () => {
    try {
      // 确保已初始化
      if (!isInitialized) {
        initialize();
      }
      
      // 如果有已删除的参数ID，优先使用最小的那个
      if (deletedParamIds.length > 0) {
        // 按数字大小排序
        deletedParamIds.sort((a, b) => a - b);
        const paramId = deletedParamIds.shift();
        usedParamIds.add(paramId);
        
        // 广播参数ID变化
        if (window.PARAM_EVENT_BUS) {
          window.PARAM_EVENT_BUS.emit('param-id-allocated', paramId);
        }
        
        return paramId.toString();
      }
      
      // 否则获取当前最大ID并加1
      let maxId = 0;
      usedParamIds.forEach(id => {
        if (id > maxId) maxId = id;
      });
      
      // 如果没有使用任何ID，从1开始
      const nextId = maxId > 0 ? maxId + 1 : 1;
      usedParamIds.add(nextId);

      // 广播参数ID变化
      if (window.PARAM_EVENT_BUS) {
        window.PARAM_EVENT_BUS.emit('param-id-allocated', nextId);
      }
      
      return nextId.toString();
    } catch (error) {
      console.error('获取下一个参数ID时出错:', error);
      // 发生错误时返回一个默认ID，确保功能可用
      return '1';
    }
  };
  
  // 记录参数使用情况
  const recordParamUsage = (paramId) => {
    if (!paramId) return;
    
    try {
      // 已初始化检查
      if (!isInitialized) {
        initialize();
      }
      
      const id = parseInt(paramId);
      if (!isNaN(id)) {
        // 记录参数ID为已使用
        usedParamIds.add(id);
        
        // 如果这个ID在已删除列表中，将其移除
        const index = deletedParamIds.indexOf(id);
        if (index !== -1) {
          deletedParamIds.splice(index, 1);
        }
        
        // 广播参数ID变化
        if (window.PARAM_EVENT_BUS) {
          window.PARAM_EVENT_BUS.emit('param-usage-recorded', id);
        }
      }
    } catch (error) {
      console.error('记录参数使用情况时出错:', error);
    }
  };
  
  // 标记参数ID为已删除(可重用)
  const markParamAsDeleted = (paramId) => {
    if (!paramId) return;
    
    try {
      // 已初始化检查
      if (!isInitialized) {
        initialize();
      }
      
      const id = parseInt(paramId);
      if (!isNaN(id) && usedParamIds.has(id)) {
        // 将ID添加到已删除列表中
        if (!deletedParamIds.includes(id)) {
          deletedParamIds.push(id);
          
          // 广播参数ID变化
          if (window.PARAM_EVENT_BUS) {
            window.PARAM_EVENT_BUS.emit('param-marked-deleted', id);
          }
        }
      }
    } catch (error) {
      console.error('标记参数为已删除时出错:', error);
    }
  };
  
  // 重置状态
  const resetState = () => {
    try {
      usedParamIds.clear();
      deletedParamIds.length = 0;
      isInitialized = false;
      
      // 广播重置事件
      if (window.PARAM_EVENT_BUS) {
        window.PARAM_EVENT_BUS.emit('param-manager-reset');
      }
    } catch (error) {
      console.error('重置参数管理器状态时出错:', error);
    }
  };
  
  // 延迟初始化 - 只有在明确调用时才执行
  const delayedInitialize = () => {
    // 如果已经初始化过，不重复初始化
    if (isInitialized) {
      return;
    }
    
    initialize();
  };
  
  // 初始化
  const initialize = () => {
    try {
      
      // 收集现有参数ID
      collectParamIdsFromDOM();
      
      // 检查GLOBAL_PARAM_NUMBERS
      if (window.GLOBAL_PARAM_NUMBERS && window.GLOBAL_PARAM_NUMBERS instanceof Set) {
        window.GLOBAL_PARAM_NUMBERS.forEach(id => {
          if (!isNaN(id) && id > 0) {
            usedParamIds.add(id);
          }
        });
      }
      
      // 检查TEMPLATE_PARAMS
      if (Array.isArray(window.TEMPLATE_PARAMS)) {
        window.TEMPLATE_PARAMS.forEach(param => {
          if (param && param.id) {
            const id = parseInt(param.id);
            if (!isNaN(id) && id > 0) {
              usedParamIds.add(id);
            }
          }
        });
      }
      
      // 确保不使用模板列表中的ID
      const templateEditor = document.querySelector('.template-editor-container');
      if (templateEditor) {
        const templateListParams = templateEditor.querySelectorAll('.template-list input.j-btn[data-param-id], .template-list span.j-btn[data-param-id]');
        const templateListIds = new Set();
        
        templateListParams.forEach(el => {
          const paramId = parseInt(el.getAttribute('data-param-id'));
          if (!isNaN(paramId)) {
            templateListIds.add(paramId);
          }
        });
        
        // 从已使用ID中移除模板列表中的ID
        templateListIds.forEach(id => {
          usedParamIds.delete(id);
        });
      }
      
      isInitialized = true;
      
      // 广播初始化完成事件
      if (window.PARAM_EVENT_BUS) {
        window.PARAM_EVENT_BUS.emit('param-manager-initialized', Array.from(usedParamIds));
      }
    } catch (error) {
      console.error('初始化参数管理器时出错:', error);
      // 确保即使出错也标记为已初始化
      isInitialized = true;
    }
  };
  
  // 立即暴露给window的函数
  window.getNextAvailableParamId = function() {
    try {
      // 确保已初始化
      if (!isInitialized) {
        initialize();
      }
      return getNextId();
    } catch (error) {
      console.error('获取下一个可用参数ID时出错:', error);
      return '1'; // 出错时默认返回1
    }
  };
  
  // 返回包含所有方法的对象
  return {
    getNextId,
    recordParamUsage,
    markParamAsDeleted,
    resetState,
    initialize,
    delayedInitialize,
    getUsedIds: () => Array.from(usedParamIds),
    getDeletedIds: () => Array.from(deletedParamIds),
    collectParamIdsFromDOM
  };
};


// 监听模板内容变化 - 更新全局变量
watch(selectedTemplateContents, (newContents) => {
  window.TEMPLATE_CONTENTS = newContents;
}, { deep: true });

// 监听模板参数变化 - 更新全局变量
watch(templateParams, (newParams) => {
  window.TEMPLATE_PARAMS = newParams;
}, { deep: true });

// 编辑模式标记
const isEditMode = ref(false);

// 打开模板编辑器
const open = async (templateData = null) => {
  // 重置编辑状态
  resetEditState();

  // 设置编辑模式状态
  isEditMode.value = false; // 新建模式默认为false

  // 设置对话框可见
  dialogVisible.value = true;

  try {
    // 初始化全局参数管理器
    initGlobalParamManager();

    // 触发模板重置事件
    dispatchTemplateReset();

    if (templateData) {
      // 编辑模式
      isEditMode.value = true;
      handleTemplateChange(templateData);
    } else {
      // 新建模式
      isEditMode.value = false;
      selectedTemplate.value = null;
      selectedContent.value = null;
      selectedTemplateContents.value = [];
      templateName.value = '';

      // 确保重置所有状态
      hasEditedContent.value = false;
      hasChanges.value = false;

      await fetchTemplateData();
    }
  } catch (error) {
    console.error('打开模板编辑器时出错:', error);
  }
};

// 打开复制模式
const openCopy = async (templateData) => {
  // 保存复制模板的原始数据，用于预览提交时回显
  copyTemplateOriginalData.value = {
    scene: templateData.scene || '',
    useId: templateData.useId || '1',
    smsExample: templateData.smsExample || '',
    aimSmsSigns: templateData.aimSmsSigns || [],
    factoryInfos: templateData.factoryInfos || null
  };

  console.log('TemplateEditor - 保存复制模板原始数据:', copyTemplateOriginalData.value);

  // 重置编辑状态
  resetEditState();

  // 设置复制模式标识（在resetEditState之后设置，避免被清除）
  window._isCopyMode = true;
  console.log('TemplateEditor - 设置复制模式标识:', window._isCopyMode);

  // 设置为新建模式（复制模式实际上是新建模式）
  isEditMode.value = false;

  // 设置对话框可见
  dialogVisible.value = true;

  try {
    // 初始化全局参数管理器
    initGlobalParamManager();

    // 触发模板重置事件
    dispatchTemplateReset();

    // 首先获取模板数据，确保左侧模板列表正确显示
    await fetchTemplateData();

    if (templateData) {
      // 复制模式：使用模板数据但保持新建模式
      // 设置模板名称
      templateName.value = templateData.templateName || '';

      // 根据复制的模板设置正确的activeTab
      if (templateData.tplType) {
        activeTab.value = templateData.tplType;
      }

      // 在左侧模板列表中找到对应的原始模板
      // 对于基础模板，应该根据cardId匹配，而不是templateName
      const originalTemplateName = templateData.templateName || '';
      const originalCardId = templateData.cardId || '';

      console.log('TemplateEditor - 复制模式：查找匹配模板', {
        originalTemplateName,
        originalCardId,
        templateDataTplType: templateData.tplType,
        availableTemplates: templates.value.map(t => ({
          templateName: t.templateName,
          cardId: t.cardId,
          tplType: t.tplType
        }))
      });

      // 优先根据cardId匹配（适用于基础模板）
      let matchingTemplate = templates.value.find(t => t.cardId === originalCardId);

      // 如果cardId匹配失败，再尝试根据templateName和tplType匹配（适用于用户自定义模板）
      if (!matchingTemplate) {
        matchingTemplate = templates.value.find(t =>
          t.templateName === originalTemplateName &&
          t.tplType === templateData.tplType
        );
      }

      console.log('TemplateEditor - 复制模式：找到匹配模板', matchingTemplate);

      // 处理复制的模板数据，确保移除templateId
      const copyTemplateData = {
        ...templateData,
        templateId: undefined // 确保没有templateId，这样就是新建模式
      };

      // 如果找到匹配的模板，使用它作为选中模板（用于左侧高亮）
      // 但使用复制的数据来设置内容和名称
      if (matchingTemplate) {
        selectedTemplate.value = {
          ...matchingTemplate,
          // 保持复制的内容数据
          pages: copyTemplateData.pages,
          contents: copyTemplateData.contents,
          // 保持原始名称，不添加后缀
          templateName: originalTemplateName
        };
      } else {
        // 如果没找到匹配的模板，直接使用复制的数据
        selectedTemplate.value = copyTemplateData;
      }

      // 设置模板名称为原始名称
      templateName.value = originalTemplateName;

      // 等待下一个tick，确保selectedTemplate的watch已经执行
      await nextTick();

      // 解析并设置模板内容
      if (copyTemplateData.pages) {
        console.log('TemplateEditor - 复制模式：解析模板内容');
        console.log('TemplateEditor - 复制模式：原始pages数据:', copyTemplateData.pages);

        // 解析pages数据（可能是字符串）
        let pages;
        if (Array.isArray(copyTemplateData.pages)) {
          pages = copyTemplateData.pages;
        } else if (typeof copyTemplateData.pages === 'string') {
          try {
            pages = JSON.parse(copyTemplateData.pages);
            console.log('TemplateEditor - 复制模式：解析pages字符串成功:', pages);
          } catch (e) {
            console.error('TemplateEditor - 复制模式：解析pages字符串失败:', e);
            pages = [];
          }
        } else {
          pages = [];
        }

        if (pages.length > 0 && pages[0].contents) {
          // 深拷贝内容并确保每个内容都有唯一ID
          const copiedContents = cloneDeep(pages[0].contents).map(content => ({
            ...content,
            contentId: content.contentId || generateUniqueId(),
            // 确保userId从模板数据中获取
            userId: copyTemplateData.userId || templateData.userId
          }));

          selectedTemplateContents.value = copiedContents;
          console.log('TemplateEditor - 复制模式：已设置模板内容:', selectedTemplateContents.value);

          // 检查设置后的内容是否包含提取的字段
          selectedTemplateContents.value.forEach(content => {
            if (content.type === 'image' || content.type === 'button') {
              console.log('TemplateEditor - 复制模式：最终内容检查:', {
                contentId: content.contentId,
                type: content.type,
                actionType: content.actionType,
                emailAddress: content.emailAddress,
                emailSubject: content.emailSubject,
                emailBody: content.emailBody,
                scheduleTitle: content.scheduleTitle,
                scheduleContent: content.scheduleContent,
                scheduleStartTimeString: content.scheduleStartTimeString,
                scheduleEndTimeString: content.scheduleEndTimeString
              });
            }
          });
        }
      }

      // 检测模板类型并进行特殊处理
      const templateType = templateFactory.detectTemplateType(selectedTemplate.value);

      // 如果是券商品模板，需要初始化券商品设置状态
      if (templateType === 'couponproduct') {
        console.log('TemplateEditor - 复制模式：检测到券商品模板，初始化设置状态');
        console.log('TemplateEditor - 复制模式：当前selectedTemplateContents:', selectedTemplateContents.value);

        // 从模板内容提取券商品数据
        const extractedData = extractCouponProductDataFromContents(selectedTemplateContents.value);
        console.log('TemplateEditor - 复制模式：提取的券商品数据:', extractedData);

        // 初始化券商品设置状态
        couponProductSettingsState.value = cloneDeep(extractedData);
        console.log('TemplateEditor - 复制模式：券商品设置状态已初始化');
      }

      // 处理复制模式下的点击事件数据
      console.log('TemplateEditor - 复制模式：处理点击事件数据');
      selectedTemplateContents.value.forEach(content => {
        if (content.type === 'image' || content.type === 'button') {
          console.log('TemplateEditor - 复制模式：处理内容点击事件:', {
            contentId: content.contentId,
            type: content.type,
            actionType: content.actionType,
            actionJson: content.actionJson,
            actionJsonType: typeof content.actionJson
          });

          // 解析actionJson字符串为对象（可能需要二次解析）
          if (content.actionJson && typeof content.actionJson === 'string') {
            try {
              content.actionJson = JSON.parse(content.actionJson);
              console.log('TemplateEditor - 复制模式：第一次解析actionJson成功:', content.actionJson);
            } catch (e) {
              console.warn('TemplateEditor - 复制模式：actionJson解析失败:', content.actionJson);
              content.actionJson = { target: '' };
            }
          }

          // 如果actionType不存在但actionJson存在，从actionJson中提取actionType
          if (!content.actionType && content.actionJson && content.actionJson.type) {
            content.actionType = ClickEventTypeConverter.toActionType(content.actionJson.type);
            console.log('TemplateEditor - 复制模式：从actionJson提取actionType:', {
              contentId: content.contentId,
              clickEventType: content.actionJson.type,
              extractedActionType: content.actionType
            });
          }

          // 调试：记录最终的actionType值
          console.log('TemplateEditor - 复制模式：最终actionType设置:', {
            contentId: content.contentId,
            type: content.type,
            finalActionType: content.actionType,
            hasActionJson: !!content.actionJson
          });

          // 确保有基本的点击事件字段
          if (!content.actionType) {
            content.actionType = 'OPEN_BROWSER';
          }

          // 使用统一的点击事件管理器来处理actionJson提取
          if (content.actionJson && typeof content.actionJson === 'object') {
            // 使用统一的toClickEventSettings方法，它会处理所有字段的提取
            const clickEventSettings = ActionJsonGenerator.toClickEventSettings(
              { type: content.actionType, ...content.actionJson },
              content.contentId,
              content.type
            );

            // 将提取的所有字段设置到content对象上
            Object.assign(content, clickEventSettings);

            console.log('TemplateEditor - 复制模式：使用统一方法提取字段:', {
              contentId: content.contentId,
              type: content.type,
              actionType: content.actionType,
              actionJson: content.actionJson,
              extractedFields: clickEventSettings
            });

            // 详细显示提取的字段
            console.log('TemplateEditor - 复制模式：详细提取字段:', JSON.stringify(clickEventSettings, null, 2));
          }

          // 确保字段有默认值
          if (!content.actionUrl) {
            content.actionUrl = '';
          }
          if (!content.actionPath) {
            content.actionPath = '';
          }

          // 初始化点击事件缓存
          const cacheKey = getCacheKey(content);
          if (!window.CLICK_EVENT_CACHE) {
            window.CLICK_EVENT_CACHE = {};
          }
          window.CLICK_EVENT_CACHE[cacheKey] = {
            actionType: content.actionType,
            actionUrl: content.actionUrl,
            actionPath: content.actionPath
          };
        }
      });
    } else {
      // 如果没有模板数据，回退到普通新建模式
      selectedTemplate.value = null;
      selectedContent.value = null;
      selectedTemplateContents.value = [];
      templateName.value = '';

      // 确保重置所有状态
      hasEditedContent.value = false;
      hasChanges.value = false;
    }

    // 在所有操作完成后，再次确认复制模式标识
    window._isCopyMode = true;
    console.log('TemplateEditor - 复制模式设置完成，最终确认标识:', window._isCopyMode);
  } catch (error) {
    console.error('打开复制模式时出错:', error);
    // 即使出错也要确保复制模式标识正确
    window._isCopyMode = true;
  }
};

// 模板变更处理
const handleTemplateChange = (newTemplate) => {
  
  // 确保模板有页面结构
  if (!newTemplate.pages || !Array.isArray(newTemplate.pages) || newTemplate.pages.length === 0) {
    newTemplate.pages = [{
      pageId: 1,
      contents: []
    }];
  }
  
  // 确保第一页有contents数组
  if (!newTemplate.pages[0].contents) {
    newTemplate.pages[0].contents = [];
  }
  
  // 检测模板类型
  const templateType = templateFactory.detectTemplateType(newTemplate);
  
  // 如果是通知类模板，使用通知类模板处理器处理内容
  if (templateType === 'notification') {
    
    // 对于所有通知类模板，都显示通知类设置面板
    showNotificationSettings.value = true;
    selectedContent.value = null; // 清空选中内容，使用专用面板
  } else {
    // 对于其他模板类型，隐藏通知类设置面板
    showNotificationSettings.value = false;
    selectedContent.value = null;
  }
  
  selectedTemplate.value = newTemplate;
};

// 获取模板数据
const fetchTemplateData = async () => {
  try {
    // 构建请求参数，包含用户上下文信息
    const params = {
      isBase: 1, 
      rows: 30,
      appKey: props.appKey || '',
      dirId: props.dirId || ''
    };
    
    const result = await api.getTemBasicList(params);
    // const result = getTemBasicList1;
    
    templates.value = processTemplatesData(result.data.list);
    
    if (templates.value.length > 0 && !activeTab.value) {
      activeTab.value = templates.value[0].tplType;
    }
  } catch (error) {
    console.error('获取模板数据失败:', error);
    ElMessage.error('获取模板数据失败');
  }
};


// 完全重置编辑状态
const resetEditState = () => {
  hasEditedContent.value = false;
  hasChanges.value = false;
  selectedTemplate.value = null;
  selectedTemplateContents.value = [];
  selectedContent.value = null;
  templateName.value = '';
  isEditMode.value = false; // 确保重置编辑模式状态
  showNotificationSettings.value = false; // 重置通知类设置面板状态

  // 重置所有模板类型的设置状态，避免数据污染
  ecommerceSettingsState.value = null;
  multiProductSettingsState.value = null;
  couponProductSettingsState.value = null;

  // 重置所有设置面板的显示状态
  showEcommerceSettings.value = false;
  showMultiProductSettings.value = false;
  showCouponProductSettings.value = false;
  showCardVoucherSettings.value = false;

  // 重置全局参数管理器
  paramUtils.resetParamManager();

  // 重置全局变量
  window.TEMPLATE_IS_REDPACKET = false;
  window.TEMPLATE_DIALOG_DATA = {};

  // 清除复制模式标识
  window._isCopyMode = false;

  // 重置全局校验失败标志，避免影响其他模板
  window._isEcommerceValidationFailed = false;
  window.CLICK_EVENT_CACHE = {};

  // 清理ClickEventSettings相关的全局状态
  window._isUpdatingContent = false;
  window._contentChanging = false;
  window._isPropsUpdate = false;
  
  if (window.GLOBAL_PARAM_NUMBERS) {
    window.GLOBAL_PARAM_NUMBERS.clear();
  }
  
};

// 处理关闭编辑器
const handleClose = () => {
  if (hasEditedContent.value || hasChanges.value) {
    // 有内容变更，显示关闭确认对话框
    closeConfirmDialogVisible.value = true;
  } else {
    // 没有内容变更，直接关闭
    dialogVisible.value = false;
    
    // 触发编辑器关闭事件，通知父组件刷新数据
    const closeEvent = new CustomEvent('template-editor-closed', {
      detail: { timestamp: Date.now() }
    });
    window.dispatchEvent(closeEvent);
  }
};

// 确认关闭编辑器
const confirmClose = () => {
  try {
    // 如果有编辑内容，保存到本地
    if (hasEditedContent.value && selectedTemplate.value) {
      try {
        saveLastEditContent();
      } catch (error) {
        console.error('保存编辑内容时出错:', error);
      }
    }
    
    // 重置全局状态
    window.IS_TEMPLATE_RESETTING = true;
    
    try {
      // 以安全方式重置参数管理器
      paramUtils.resetParamManager();
    } catch (paramError) {
      console.error('重置参数管理器时出错:', paramError);
    }
    
    try {
      // 分发模板重置事件
      dispatchTemplateReset();
    } catch (eventError) {
      console.error('分发模板重置事件时出错:', eventError);
    }
    
    // 关闭对话框
    closeConfirmDialogVisible.value = false;
    dialogVisible.value = false;
    
    // 以安全方式重置编辑状态
    try {
      resetEditState();
    } catch (resetError) {
      console.error('重置编辑状态时出错:', resetError);
      
      // 如果重置失败，确保基本状态被重置
      hasEditedContent.value = false;
      hasChanges.value = false;
      selectedTemplate.value = null;
      selectedContent.value = null;
    }
    
    // 触发编辑器关闭事件，通知父组件刷新数据
    const closeEvent = new CustomEvent('template-editor-closed', {
      detail: { timestamp: Date.now() }
    });
    window.dispatchEvent(closeEvent);
    
    // 重置完成后清除标记
    setTimeout(() => {
      window.IS_TEMPLATE_RESETTING = false;
    }, 100);
  } catch (error) {
    console.error('关闭编辑器时发生错误:', error);
    // 确保对话框关闭，即使发生错误
    closeConfirmDialogVisible.value = false;
    dialogVisible.value = false;
  }
};

// 取消关闭编辑器
const cancelClose = () => {
  closeConfirmDialogVisible.value = false;
};

// 处理标签切换
const handleTabChange = (tabName) => {
  activeTab.value = tabName;
};

// 切换模板
const handleTemplateClick = (template) => {

  // 清除复制模式标识，切换模板后使用新建模板的逻辑
  window._isCopyMode = false;
  console.log('模板切换：清除复制模式标识，使用新建模板逻辑');

  // 检查是否是切换到不同的模板（无论是否编辑过内容，都需要确认）
  if (selectedTemplate.value && selectedTemplate.value.templateId !== template.templateId) {
    // 保存待切换的模板
    pendingTemplateChange.value = template;
    // 显示确认对话框
    switchTemplateDialogVisible.value = true;
  } else {
    // 如果是同一个模板或者没有选中模板，直接切换
    // 创建一个新的模板对象，移除templateId以确保是新建模式
    const newTemplate = { ...template };
    if (!isEditMode.value) {
      delete newTemplate.templateId; // 在新建模式下移除templateId
    }
    
    // 检测模板类型并处理
    const templateType = templateFactory.detectTemplateType(newTemplate);
    console.log('检测到的模板类型:', templateType);
    
    // 清除红包模板标记（在检测模板类型之后）
    window.TEMPLATE_IS_REDPACKET = false;
    
    // 如果是通知类模板，使用通知类模板处理器处理内容
    if (templateType === 'notification') {
      // 确保模板有页面结构
      if (!newTemplate.pages || !Array.isArray(newTemplate.pages) || newTemplate.pages.length === 0) {
        newTemplate.pages = [{
          pageId: 1,
          contents: []
        }];
      }
      
      // 确保第一页有contents数组
      if (!newTemplate.pages[0].contents) {
        newTemplate.pages[0].contents = [];
      }
   
    }
    
    // 应用模板变更
    const result = applyTemplateChange(newTemplate);
    
    if (result && templateType === 'notification') {
      // 初始化通知设置
      if (newTemplate.templateName?.includes('增强')) {
        // 增强通知类默认设置：第一对参数固定显示，设置面板控制额外的2对参数
        notificationSettings.maxVisibleParams = 2;
        notificationSettings.maxVisibleButtons = 2;
      } else {
        // 一般通知类默认设置
        notificationSettings.maxVisibleParams = 2;
        notificationSettings.maxVisibleButtons = 2;
      }
      
      // 确保内容数量与设置同步
      nextTick(() => {
        // 对于增强类模板，需要确保有足够的参数对（第一对固定+额外参数对）
        const totalParamsNeeded = newTemplate.templateName?.includes('增强') 
          ? 1 + notificationSettings.maxVisibleParams  // 第一对固定 + 额外参数对
          : notificationSettings.maxVisibleParams;     // 一般模板直接使用设置值
        
        ensureParamPairs(newTemplate, totalParamsNeeded);
        ensureButtons(newTemplate, notificationSettings.maxVisibleButtons);
        
        // 强制更新预览组件
        selectedTemplateContents.value = [...selectedTemplateContents.value];
      });
      
      // 对于所有通知类模板，都显示通知类设置面板
      showNotificationSettings.value = true;
      selectedContent.value = null; // 清空选中内容，使用专用面板
    }
    // 如果是电商模板，进行特殊处理
    else if (templateType === 'ecommerce') {
      
      // 确保模板数据正确加载后再进行处理
      nextTick(() => {
        // 从模板API数据中提取电商数据
        const extractedData = EcommerceTemplate.extractEcommerceDataFromContents(selectedTemplateContents.value)
        console.log('TemplateEditor - 电商模板切换，从API数据提取的电商数据:', extractedData);
        
        // 使用提取的数据初始化电商设置状态
        ecommerceSettingsState.value = EcommerceTemplate.initializeEcommerceSettingsState(extractedData);
        
        // 不立即显示电商设置面板，等待用户点击中间预览区域时再显示
        showEcommerceSettings.value = false;
        selectedContent.value = null;
      });
    }
    // 如果是多商品模板，进行特殊处理
    else if (templateType === 'multiproduct') {
      // 先重置多商品设置状态
      multiProductSettingsState.value = null;

      // 确保模板数据正确加载后再进行处理
      nextTick(() => {
        // 模板切换时，总是重新初始化内容，避免被其他模板数据污染
        console.log('TemplateEditor - 多商品模板切换，强制重新初始化内容以避免数据污染');
        const templateHandler = templateFactory.getTemplateHandler('multiproduct');
        if (templateHandler && templateHandler.initializeContents) {
          const initialContents = templateHandler.initializeContents(selectedTemplate.value);
          selectedTemplateContents.value = cloneDeep(initialContents).map(content => ({
            ...content,
            contentId: content.contentId || generateUniqueId(),
            userId: selectedTemplate.value.userId
          }));
          console.log('TemplateEditor - 多商品模板切换，重新初始化的内容:', selectedTemplateContents.value);
        }

        // 从重新初始化的内容中提取多商品数据
        const extractedData = extractMultiProductDataFromContents(selectedTemplateContents.value);
        console.log('TemplateEditor - 多商品模板切换，从重新初始化的内容提取的数据:', extractedData);

        // 使用提取的数据初始化多商品设置状态
        initializeMultiProductSettingsState(extractedData);

        // 不立即显示多商品设置面板，等待用户点击中间预览区域时再显示
        showMultiProductSettings.value = false;
        selectedContent.value = null;
      });
    }
    // 如果是券+商品模板，进行特殊处理
    else if (templateType === 'couponproduct') {
      // 先重置券+商品设置状态
      couponProductSettingsState.value = null;

      // 确保模板数据正确加载后再进行处理
      nextTick(() => {
        // 重新确保 selectedTemplateContents 是正确的券+商品模板数据
        const templateHandler = templateFactory.getTemplateHandler('couponproduct');
        if (templateHandler && templateHandler.initializeContents) {
          const initialContents = templateHandler.initializeContents(selectedTemplate.value);
          selectedTemplateContents.value = cloneDeep(initialContents).map(content => ({
            ...content,
            contentId: content.contentId || generateUniqueId(),
            userId: selectedTemplate.value.userId
          }));
        }

        // 从模板API数据中提取券+商品数据
        const extractedData = extractCouponProductDataFromContents(selectedTemplateContents.value);
        console.log('TemplateEditor - 券+商品模板切换，从API数据提取的券+商品数据:', extractedData);
        
        // 使用提取的数据初始化券+商品设置状态
        initializeCouponProductSettingsState(extractedData);
        
        // 不立即显示券+商品设置面板，等待用户点击中间预览区域时再显示
        showCouponProductSettings.value = false;
        selectedContent.value = null;
      });
    }
    // 如果是单卡券模板，进行特殊处理
    else if (templateType === 'cardvoucher') {

      // 确保模板数据正确加载后再进行处理
      nextTick(() => {
        // 重新确保 selectedTemplateContents 是正确的单卡券模板数据
        const templateHandler = templateFactory.getTemplateHandler('cardvoucher');
        if (templateHandler && templateHandler.initializeContents) {
          const initialContents = templateHandler.initializeContents(selectedTemplate.value);
          selectedTemplateContents.value = cloneDeep(initialContents).map(content => ({
            ...content,
            contentId: content.contentId || generateUniqueId(),
            userId: selectedTemplate.value.userId
          }));
          console.log('TemplateEditor - nextTick中重新初始化单卡券模板内容:', selectedTemplateContents.value.length, '个内容');
        }

        // 不立即显示单卡券设置面板，等待用户点击中间预览区域时再显示
        showCardVoucherSettings.value = false;
        selectedContent.value = null;
      });
    }

    // 当选择模板时，从基础模板列表中查找对应的subType
    const findBaseTemplate = () => {
      try {
        // 直接从templates中查找对应的基础模板
        const baseTemplate = templates.value.find(t => 
          t.cardId === template.cardId && 
          t.templateName?.includes('增强') === template.templateName?.includes('增强')
        );
        
        if (baseTemplate) {
          console.log('handleTemplateClick: 准备发送 update-sub-type 事件，从基础模板获取的subType:', baseTemplate.subType);
          emit('update-sub-type', String(baseTemplate.subType));
        } else {
          console.warn('未找到匹配的基础模板');
          emit('update-sub-type', '');
        }
      } catch (error) {
        console.error('获取基础模板subType失败:', error);
        emit('update-sub-type', '');
      }
    };

    // 调用获取基础模板subType的函数
    findBaseTemplate();
  }
  selectedContent.value = null; // 切换模板时清空
};

// 检查是否真正编辑过内容
const hasActuallyEditedContent = () => {
  // 如果没有选择任何模板，则没有编辑过内容
  if (!selectedTemplate.value) return false;
  
  // 如果用户明确修改了内容（如修改了文本、上传了图片等）
  if (hasEditedContent.value) return true;
  
  // 检查是否有任何内容被修改过
  if (selectedTemplateContents.value.length === 0) return false;
  
  // 检查是否至少有一个内容项被修改过
  const hasModifiedContent = selectedTemplateContents.value.some(content => {
    if (content.isEditing) return true; // 内容处于编辑状态
    if (content.editContent) return true; // 有编辑过的内容
    if (content.originalContent && content.content !== content.originalContent) return true; // 内容与原始内容不同
    
    return false;
  });
  
  return hasModifiedContent || hasChanges.value;
};

// 确认切换模板
const confirmSwitchTemplate = () => {
  if (selectedTemplate.value) {
    // 清除旧模板的点击事件缓存（按模板ID隔离）
    const oldTemplateId = selectedTemplate.value.templateId;
    if (window.CLICK_EVENT_CACHE?.[oldTemplateId]) {
      delete window.CLICK_EVENT_CACHE[oldTemplateId];
    }
    // 强制清空当前选中内容的点击事件字段
    selectedContent.value = null;
    selectedTemplateContents.value = selectedTemplateContents.value.map(content => ({
      ...content,
      actionJson: "OPEN_BROWSER,OPEN_APP,OPEN_URL,OPEN_QUICK,DIAL_PHONE,COPY_PARAMETER, OPEN_SMS,OPEN_EMAIL,OPEN_SCHEDULE,OPEN_POPUP",
      clickEvent: null
    }));
    // 重置全局状态标记
    window._isUpdatingContent = false;
    window._contentChanging = false;

    // 清除复制模式标识，切换模板后使用新建模板的逻辑
    window._isCopyMode = false;
    console.log('模板切换：清除复制模式标识，使用新建模板逻辑');
  }
  if (pendingTemplateChange.value) {
    // 创建一个新的模板对象，移除templateId以确保是新建模式
    const newTemplate = cloneDeep(pendingTemplateChange.value);
    if (!isEditMode.value) {
      delete newTemplate.templateId; // 在新建模式下移除templateId
    }
    
    // 当确认切换模板时，从基础模板列表中查找对应的subType
    const findBaseTemplateForSwitch = () => {
      try {
        console.log('当前切换的模板cardId:', newTemplate.cardId);
        console.log('所有基础模板数据:', templates.value);
        
        // 直接从templates中查找对应的基础模板
        const baseTemplate = templates.value.find(t => 
          t.cardId === newTemplate.cardId && 
          t.templateName?.includes('增强') === newTemplate.templateName?.includes('增强')
        );
        
        if (baseTemplate) {
          console.log('confirmSwitchTemplate: 准备发送 update-sub-type 事件，从基础模板获取的subType:', baseTemplate.subType);
          emit('update-sub-type', String(baseTemplate.subType));
        } else {
          console.warn('未找到匹配的基础模板');
          emit('update-sub-type', '');
        }
      } catch (error) {
        console.error('获取基础模板subType失败:', error);
        emit('update-sub-type', '');
      }
    };

    // 调用获取基础模板subType的函数
    findBaseTemplateForSwitch();
    
    // 检测模板类型并处理
    const templateType = templateFactory.detectTemplateType(newTemplate);
    console.log('检测到的模板类型:', templateType);
    
    // 清除红包模板标记（在检测模板类型之后）
    window.TEMPLATE_IS_REDPACKET = false;
    
    // 如果是通知类模板，使用通知类模板处理器处理内容
    if (templateType === 'notification') {
      // 确保模板有页面结构
      if (!newTemplate.pages || !Array.isArray(newTemplate.pages) || newTemplate.pages.length === 0) {
        newTemplate.pages = [{
          pageId: 1,
          contents: []
        }];
      }
      
      // 确保第一页有contents数组
      if (!newTemplate.pages[0].contents) {
        newTemplate.pages[0].contents = [];
      }
    }
    
    // 应用模板变更
    const result = applyTemplateChange(newTemplate);
    
    if (result && templateType === 'notification') {
      // 初始化通知设置
      if (newTemplate.templateName?.includes('增强')) {
        // 增强通知类默认设置：第一对参数固定显示，设置面板控制额外的2对参数
        notificationSettings.maxVisibleParams = 2;
        notificationSettings.maxVisibleButtons = 2;
      } else {
        // 一般通知类默认设置
        notificationSettings.maxVisibleParams = 2;
        notificationSettings.maxVisibleButtons = 2;
      }
      
      // 确保内容数量与设置同步
      nextTick(() => {
        // 对于增强类模板，需要确保有足够的参数对（第一对固定 + 额外参数对）
        const totalParamsNeeded = newTemplate.templateName?.includes('增强') 
          ? 1 + notificationSettings.maxVisibleParams  // 第一对固定 + 额外参数对
          : notificationSettings.maxVisibleParams;     // 一般模板直接使用设置值
        
        ensureParamPairs(newTemplate, totalParamsNeeded);
        ensureButtons(newTemplate, notificationSettings.maxVisibleButtons);
        
        // 强制更新预览组件
        selectedTemplateContents.value = [...selectedTemplateContents.value];
        
       
      });
      
      // 对于所有通知类模板，都显示通知类设置面板
      showNotificationSettings.value = true;
      selectedContent.value = null; // 清空选中内容，使用专用面板
    }
    // 如果是电商模板，进行特殊处理
    else if (result && templateType === 'ecommerce') {
      // 确保模板数据正确加载后再进行处理
      nextTick(() => {
        // 从模板API数据中提取电商数据
        const extractedData = EcommerceTemplate.extractEcommerceDataFromContents(selectedTemplateContents.value);
        console.log('TemplateEditor - 确认切换电商模板，从API数据提取的电商数据:', extractedData);
        
        // 使用提取的数据初始化电商设置状态
        ecommerceSettingsState.value = EcommerceTemplate.initializeEcommerceSettingsState(extractedData);
        
        // 不立即显示电商设置面板，等待用户点击中间预览区域时再显示
        showEcommerceSettings.value = false;
        selectedContent.value = null;
      });
    }
    // 如果是多商品模板，进行特殊处理
    else if (result && templateType === 'multiproduct') {
       // 先重置多商品设置状态
      multiProductSettingsState.value = null;

      // 确保模板数据正确加载后再进行处理
      nextTick(() => {
        // 模板切换时，总是重新初始化内容，避免被其他模板数据污染
        console.log('TemplateEditor - 确认切换多商品模板，强制重新初始化内容以避免数据污染');

        // 使用完整的模板对象（保留pages数据）重新初始化
        const templateHandler = templateFactory.getTemplateHandler('multiproduct');
        if (templateHandler && templateHandler.initializeContents) {
          const initialContents = templateHandler.initializeContents(selectedTemplate.value);
          if (initialContents && initialContents.length > 0) {
            selectedTemplateContents.value = cloneDeep(initialContents).map(content => ({
              ...content,
              contentId: content.contentId || generateUniqueId(),
              userId: selectedTemplate.value.userId
            }));
            console.log('TemplateEditor - 确认切换多商品模板，重新初始化的内容:', selectedTemplateContents.value);

            // 从重新初始化的内容中提取多商品数据
            const extractedData = extractMultiProductDataFromContents(selectedTemplateContents.value);
            console.log('TemplateEditor - 确认切换多商品模板，从重新初始化内容提取的多商品数据:', extractedData);
            initializeMultiProductSettingsState(extractedData);
          } else {
            // 如果仍然没有内容，从空内容提取数据（MultiProductTemplate会提供默认数据）
            console.log('TemplateEditor - 确认切换多商品模板没有API数据，使用默认数据结构');
            const extractedData = extractMultiProductDataFromContents([]);
            initializeMultiProductSettingsState(extractedData);
          }
        }

        // 不立即显示多商品设置面板，等待用户点击中间预览区域时再显示
        showMultiProductSettings.value = false;
        selectedContent.value = null;
      });
    }
    // 如果是券+商品模板，进行特殊处理
    else if (result && templateType === 'couponproduct') {
       // 先重置券+商品设置状态
      couponProductSettingsState.value = null;
      
      // 确保模板数据正确加载后再进行处理
      nextTick(() => {
        // 从模板API数据中提取券+商品数据
        const extractedData = extractCouponProductDataFromContents(selectedTemplateContents.value);
        console.log('TemplateEditor - 确认切换券+商品模板，从API数据提取的券+商品数据:', extractedData);
        
        // 使用提取的数据初始化券+商品设置状态
        initializeCouponProductSettingsState(extractedData);
        
        // 不立即显示券+商品设置面板，等待用户点击中间预览区域时再显示
        showCouponProductSettings.value = false;
        selectedContent.value = null;
      });
    }
    
    pendingTemplateChange.value = null;
  } else {
    console.warn('没有待切换的模板');
  }
  
  switchTemplateDialogVisible.value = false;
  selectedContent.value = null; // 切换模板时清空

  // 重置全局校验失败标志，避免影响切换后的模板
  window._isEcommerceValidationFailed = false;
};

// 取消切换模板
const cancelSwitchTemplate = () => {
  pendingTemplateChange.value = null;
  switchTemplateDialogVisible.value = false;
};

// 选中内容
const selectContent = (content) => {
  // 如果之前有选中的内容且是同一个内容，不做任何处理
  if (selectedContent.value === content) {
    return;
  }
  
  try {
    // 如果之前有选中的内容且不是同一个内容，保存其设置
    if (selectedContent.value) {
      saveContentSettings(selectedContent.value);
    }
    
    // 设置选中的内容
    selectedContent.value = content;
    
    // 如果是通知类模板，根据内容类型决定是否显示通知类设置
    if (isNotificationTemplate.value) {
      // 对于通知类模板，点击任何内容都隐藏通知类设置面板，显示对应的内容设置面板
      showNotificationSettings.value = false;
    } else {
      // 非通知类模板，隐藏通知类设置面板
      showNotificationSettings.value = false;
    }
    
    // 确保点击事件缓存中存在该类型的缓存
    if (!window.CLICK_EVENT_CACHE) {
      window.CLICK_EVENT_CACHE = {};
    }
    
    if (content.type) {
      // 只为需要点击事件的内容类型（图片和按钮）创建缓存
      if (content.type === 'image' || content.type === 'button') {
        // 使用更精确的缓存键，包含contentId和positionNumber来区分不同的内容
        const cacheKey = getCacheKey(content);
        
        // 确保缓存对象存在，如果不存在则创建
        if (!window.CLICK_EVENT_CACHE[cacheKey]) {
          window.CLICK_EVENT_CACHE[cacheKey] = {
            actionType: content.actionType || 'OPEN_BROWSER',
            actionUrl: content.actionUrl || '',
            actionPath: content.actionPath || ''
          };
        } else {
          // 如果内容本身已有设置，优先使用内容自身的设置覆盖缓存
          if (content.actionType || content.actionUrl || content.actionPath) {
            window.CLICK_EVENT_CACHE[cacheKey] = {
              actionType: content.actionType || window.CLICK_EVENT_CACHE[cacheKey].actionType || 'OPEN_BROWSER',
              actionUrl: content.actionUrl || window.CLICK_EVENT_CACHE[cacheKey].actionUrl || '',
              actionPath: content.actionPath || window.CLICK_EVENT_CACHE[cacheKey].actionPath || ''
            };
          }
        }
      }
    }
  } catch (error) {
    console.error('选中内容时出错:', error);
  }
};

// 生成缓存键的辅助函数
const getCacheKey = (content) => {
  if (!content || !content.type) return '';
  
  // 对于图片类型，使用positionNumber来区分不同的图片
  if (content.type === 'image') {
    return `${content.type}_${content.positionNumber || content.contentId || 'default'}`;
  }
  
  // 对于按钮类型，也使用positionNumber来区分
  if (content.type === 'button') {
    return `${content.type}_${content.positionNumber || content.contentId || 'default'}`;
  }
  
  // 其他类型使用contentId
  return `${content.type}_${content.contentId || 'default'}`;
};

// 处理内容更新
const updateContent = (newContent) => {
  // 检查newContent是否为有效值
  if (!newContent) {
    console.warn('更新内容时接收到无效值');
    return;
  }

  // 特殊处理：电商设置更新
  if (newContent.type === 'ecommerce-settings' && newContent.isEcommerceSettings) {
    handleEcommerceSettingsChange(newContent);
    return;
  }

  // 特殊处理：多商品设置更新
  if (newContent.type === 'multi-product-settings' && newContent.isMultiProductSettings) {
    handleMultiProductSettingsChange(newContent);
    return;
  }

  // 特殊处理：券+商品设置更新
  if (newContent.type === 'coupon-product-settings' && newContent.isCouponProductSettings) {
    handleCouponProductSettingsChange(newContent);
    return;
  }

  // 特殊调试：轮播图内容更新
  if (newContent.carouselImages) {
    console.log('TemplateEditor - 接收到轮播图内容更新:', {
      contentId: newContent.contentId,
      carouselImages: newContent.carouselImages,
      轮播图片数量: newContent.carouselImages.length
    });
  }

  // 标记为已更改
  hasChanges.value = true;
  hasEditedContent.value = true;
  
  try {
    // 处理特殊的批量更新类型（来自LongTextTemplateRenderer）
    if (newContent.type === 'update-contents' && newContent.contents) {
      console.log('处理批量内容更新:', newContent.contents);
      selectedTemplateContents.value = [...newContent.contents];
      return;
    }
    
    // 处理添加按钮类型（来自LongTextTemplateRenderer）
    if (newContent.type === 'add-button' && newContent.buttonData) {
      console.log('添加新按钮:', newContent.buttonData);
      selectedTemplateContents.value.push(newContent.buttonData);
      return;
    }
    
    // 查找并更新内容
    const contentIndex = selectedTemplateContents.value.findIndex(
      item => item.contentId === newContent.contentId
    );
    
    console.log('TemplateEditor - 查找内容索引:', {
      查找的contentId: newContent.contentId,
      找到的索引: contentIndex,
      当前所有内容: selectedTemplateContents.value.map(item => ({
        contentId: item.contentId,
        type: item.type,
        hasCarouselImages: !!item.carouselImages
      }))
    });
    
    if (contentIndex !== -1) {
      // 深拷贝一份，避免引用问题
      const updatedContent = cloneDeep(newContent);
      
      // 如果是图片类型内容，检查是否真的发生了修改
      if (updatedContent.type === 'image' || updatedContent.type === 'background' || updatedContent.type === 'video') {
        const originalContent = selectedTemplateContents.value[contentIndex];
        // 如果src发生了变化，标记为已修改
        if (updatedContent.src !== originalContent.defaultSrc) {
          // console.log('图片内容已修改，原始路径:', originalContent.defaultSrc, '新路径:', updatedContent.src);
          updatedContent.userEdited = true;
        }
      }
      
      // 特殊调试：轮播图内容更新
      if (updatedContent.carouselImages) {
        console.log('TemplateEditor - 更新轮播图内容到索引', contentIndex, {
          更新前: selectedTemplateContents.value[contentIndex],
          更新后: updatedContent
        });
      }
      
      selectedTemplateContents.value[contentIndex] = updatedContent;
      
      // 如果更新的是当前选中的内容，也更新selectedContent引用
      if (selectedContent.value && selectedContent.value.contentId === newContent.contentId) {
        selectedContent.value = updatedContent;
        console.log('TemplateEditor - 也更新了selectedContent引用');
      }
      
      // 特殊调试：轮播图内容更新后验证
      if (updatedContent.carouselImages) {
        console.log('TemplateEditor - 轮播图内容更新完成，验证结果:', {
          更新后的内容: selectedTemplateContents.value[contentIndex],
          轮播图片数量: selectedTemplateContents.value[contentIndex].carouselImages?.length
        });
      }
      
      // 更新点击事件缓存
      if (updatedContent.type && (updatedContent.type === 'image' || updatedContent.type === 'button')) {
        if (!window.CLICK_EVENT_CACHE) {
          window.CLICK_EVENT_CACHE = {};
        }
        
        // 使用新的缓存键机制
        const cacheKey = getCacheKey(updatedContent);
        
        if (!window.CLICK_EVENT_CACHE[cacheKey]) {
          window.CLICK_EVENT_CACHE[cacheKey] = {};
        }
        
        window.CLICK_EVENT_CACHE[cacheKey] = {
          actionType: updatedContent.actionType || 'OPEN_BROWSER',
          actionUrl: updatedContent.actionUrl || '',
          actionPath: updatedContent.actionPath || ''
        };
      }
    } else {
      console.warn('TemplateEditor - 未找到要更新的内容:', newContent.contentId);
      console.log('TemplateEditor - 当前所有内容的contentId:', selectedTemplateContents.value.map(item => item.contentId));
    }
  } catch (error) {
    console.error('更新内容时出错:', error);
  }
};

// 处理右侧设置面板的内容更新
const handleContentSettingsUpdate = (content) => {
  console.log('TemplateEditor - 接收到内容设置更新:', content);
  
  if (!content) {
    console.log('TemplateEditor - 内容设置更新：内容为空');
    return;
  }

  // 如果是多商品设置，使用专门的处理函数
  if (isMultiProductTemplate.value && content.type === 'multi-product-settings' && content.isMultiProductSettings) {
    console.log('TemplateEditor - 处理多商品设置更新');
    handleMultiProductSettingsChange(content);
    return;
  }
  
  // 如果是券+商品设置，使用专门的处理函数
  if (isCouponProductTemplate.value && content.type === 'coupon-product-settings' && content.isCouponProductSettings) {
    console.log('TemplateEditor - 处理券+商品设置更新');
    handleCouponProductSettingsChange(content);
    return;
  }
  
  // 对于其他所有内容，使用常规的更新逻辑
  updateContent(content);
  
  // 查找并更新对应的内容
  const index = selectedTemplateContents.value.findIndex(item => 
    item.type === content.type && item.positionNumber === content.positionNumber
  );
  
  if (index !== -1) {
    // 更新找到的内容
    selectedTemplateContents.value[index] = { ...selectedTemplateContents.value[index], ...content };
    
    // 创建新的数组引用以触发响应式更新
    selectedTemplateContents.value = [...selectedTemplateContents.value];
    
    // 标记有变化
    hasChanges.value = true;
    hasEditedContent.value = true;
    
    console.log('TemplateEditor - 内容已更新，新的selectedTemplateContents:', selectedTemplateContents.value);
  } else {
    console.log('TemplateEditor - 未找到要更新的内容');
  }
};

// 关闭右侧面板
const closeRightPanel = () => {
  selectedContent.value = null;
  showNotificationSettings.value = false;
  showMultiTextSettings.value = false;
};

// 处理参数插入
const handleParamInsert = (paramInfo) => {
  // 处理参数插入逻辑

};

// 处理参数管理
const handleParamManage = () => {
  try {
    // console.log('处理参数管理...');
    
    // 在打开参数管理前，确保参数管理器已初始化
    if (!window.GLOBAL_PARAM_MANAGER) {
      console.warn('全局参数管理器不存在，尝试初始化');
      initGlobalParamManager();
    }
    
    // 确保initialize方法存在
    if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.initialize === 'function') {
      try {
        window.GLOBAL_PARAM_MANAGER.initialize();
        // console.log('参数管理器初始化成功');
      } catch (error) {
        console.error('调用参数管理器initialize方法时出错:', error);
        // 尝试修复参数管理器
        window.GLOBAL_PARAM_MANAGER = createGlobalParamManager();
      }
    } else {
      console.warn('参数管理器的initialize方法不可用，尝试重新创建');
      
      // 尝试重新初始化
      try {
        initGlobalParamManager();
      } catch (e) {
        console.error('重新初始化参数管理器失败:', e);
      }
    }
    
    // 无论如何，确保能够打开参数管理对话框
  paramManageVisible.value = true;
  } catch (error) {
    console.error('处理参数管理时出错:', error);
    // 仍然尝试打开对话框
    paramManageVisible.value = true;
  }
};

// 处理参数管理确认
const handleParamManageConfirm = (updatedParams) => {
  templateParams.value = updatedParams;
  
  // 更新全局参数管理器状态
  if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.markParamAsDeleted === 'function') {
    try {
      // 找出已删除的参数
      const oldParamIds = new Set(templateParams.value.map(p => parseInt(p.id)).filter(id => !isNaN(id)));
      const newParamIds = new Set(updatedParams.map(p => parseInt(p.id)).filter(id => !isNaN(id)));
      
      // 找出在old中但不在new中的ID，这些是被删除的
      oldParamIds.forEach(id => {
        if (!newParamIds.has(id)) {
          window.GLOBAL_PARAM_MANAGER.markParamAsDeleted(id);
        }
      });
    } catch (e) {
      console.error('更新参数管理器状态时出错:', e);
    }
  }
  
  paramManageVisible.value = false;
};

// 保存模板
const saveTemplate = async () => {
  if (onSave) {
    onSave(editedData);
  }
  emit('save', editedData);
};
// 统一点击事件结构转换工具
function normalizeAllClickEvents(contents) {
  if (!contents) return;
  contents.forEach(item => {
    if (item.clickEvent) {
      // 已经是新结构就跳过
      if (
        typeof item.clickEvent.actionType === 'string' &&
        typeof item.clickEvent.actionUrl === 'string'
      ) {
        // 已是新结构，什么都不做
      } else if (item.clickEvent.type) {
        // 老结构才升级
        const actionType = ClickEventTypeConverter.toActionType(item.clickEvent.type);
        const actionUrl = ActionJsonGenerator.fromClickEvent(item.clickEvent).target;
        item.clickEvent = { actionType, actionUrl };
      }
      // 其他情况（比如 clickEvent 为空对象），可以根据实际需求补充处理
    }
    if (item.children && Array.isArray(item.children)) {
      normalizeAllClickEvents(item.children);
    }
  });
}
// 提交模板
const submitTemplate = async () => {
  try {
    
    // 保存当前设置面板状态
    const currentSelectedContent = selectedContent.value;
    
    // 保存所有内容的设置
    if (currentSelectedContent) {
      saveContentSettings(currentSelectedContent);
    }
    
    // 强制同步缓存中的数据到内容对象
    syncCacheToContents();
    
    // 1. 第一步：检查模板名称是否为空
    if (!isEditMode.value) { 
      if (!templateName.value || templateName.value.trim() === '') {
        ElMessage.warning('请输入模板名称');
        // 焦点定位到模板名称输入框
        const templateNameInput = document.querySelector('.template-name-editor input');
        if (templateNameInput) {
          templateNameInput.focus();
        }
        return;
      }
    } else {
      if (templateName.value.trim() === '' && hasChanges.value) {
        ElMessage.warning('模板名称不能为空');
        return;
      }
    }
    
    // 检查是否选择了模板
    if (!selectedTemplate.value) {
      ElMessage.warning('请选择版式类型');
      return;
    }
    
    // 电商模板特殊校验
    if (isEcommerceTemplate.value) {
      console.log('TemplateEditor - 电商模板专用校验开始');
      
      // 检查电商设置状态是否初始化
      if (!ecommerceSettingsState.value) {
        ElMessage.warning('电商设置未初始化，请先配置电商内容');
        
        // 设置校验失败标志，防止重新初始化用户输入
        window._isEcommerceValidationFailed = true;
        setTimeout(() => {
          window._isEcommerceValidationFailed = false;
        }, 1000); // 1秒后清除标志
        
        showEcommerceSettings.value = true; // 显示电商设置面板
        selectedContent.value = ecommerceSettingsContent.value;
        return;
      }
      
      // 获取电商显示数据进行校验
      const ecommerceDisplayData = getEcommerceDisplayData();
      normalizeAllClickEvents(ecommerceDisplayData.images);
      normalizeAllClickEvents(ecommerceDisplayData.buttons);
      
      console.log('TemplateEditor - 电商校验数据:', ecommerceDisplayData);
      
      // 添加详细的数据结构检查
      console.log('TemplateEditor - 电商校验数据结构检查:', {
        hasImages: !!ecommerceDisplayData.images,
        imagesLength: ecommerceDisplayData.images?.length || 0,
        hasButtons: !!ecommerceDisplayData.buttons,
        buttonsLength: ecommerceDisplayData.buttons?.length || 0,
        images: ecommerceDisplayData.images,
        buttons: ecommerceDisplayData.buttons
      });
      
      
      
      const settings = ecommerceSettingsContent.value.currentData;
      if (settings.style === 'video') {
        // 视频模式：只校验视频
        if (!settings.videoSrc || settings.videoSrc.includes('defaultVideo.jpg')) {
          ElMessage.warning('请上传视频');
          
          // 设置校验失败标志，防止重新初始化用户输入
          window._isEcommerceValidationFailed = true;
          setTimeout(() => {
            window._isEcommerceValidationFailed = false;
          }, 1000); // 1秒后清除标志
          
          showEcommerceSettings.value = true;
          selectedContent.value = ecommerceSettingsContent.value;
          return;
        }
      } else {
        // 校验每张图片是否为默认图片，必须逐个检查，不能使用some()方法
        for (let i = 0; i < ecommerceDisplayData.images.length; i++) {
          const image = ecommerceDisplayData.images[i];
          console.log(`TemplateEditor - 检查第${i + 1}张电商模板图片:`, {
            src: image.src,
            包含aim_files_aim_defult: image.src?.includes('/aim_files/aim_defult/'),
            包含defaultImg: image.src?.includes('defaultImg'),
            包含defaultImg48_65: image.src?.includes('defaultImg48_65.jpg')
          });
          
          // 电商模板要求用户上传真实图片，不接受任何默认图片
          // 包括defaultImg.jpg、defaultImg48_65.jpg等都不被接受，要求用户上传图片
          const isDefaultImage = !image.src ||
            image.src.trim() === '' ||
            image.src === null ||
            image.src === undefined ||
            image.src.includes('/aim_files/aim_defult/') ||
            image.src.includes('defaultImg');

          // 复制模式特殊处理：只有确实是默认图片才校验
          const isCopyMode = window._isCopyMode || false;
          const shouldValidateImage = isCopyMode ?
            (isDefaultImage && (
              !image.src ||
              image.src.includes('/aim_files/aim_defult/') ||
              image.src.includes('defaultImg')
            )) :
            isDefaultImage;

          if (shouldValidateImage) {
            console.log(`TemplateEditor - 第${i + 1}张图片校验失败：图片为默认图片，需要用户上传图片`);
            ElMessage.warning(`请上传图片第${i + 1}张图片`);
            
            // 设置校验失败标志，防止重新初始化用户输入
            window._isEcommerceValidationFailed = true;
            setTimeout(() => {
              window._isEcommerceValidationFailed = false;
            }, 1000); // 1秒后清除标志
            
            showEcommerceSettings.value = true;
            selectedContent.value = ecommerceSettingsContent.value;
            console.log('TemplateEditor - showEcommerceSettings 设置为 true，selectedContent 设置为电商设置');
            return;
          }
        }
        
        console.log('TemplateEditor - 所有电商模板图片校验通过，都不是默认图片');
        console.log('TemplateEditor - 开始校验图片点击事件，图片数量:', ecommerceDisplayData.images.length);
        
        // 校验图片点击事件
        for (let i = 0; i < ecommerceDisplayData.images.length; i++) {
          const image = ecommerceDisplayData.images[i];
          console.log(`TemplateEditor - 校验第${i + 1}张图片点击事件:`, {
            hasClickEvent: !!image.clickEvent,
            clickEventType: image.clickEvent?.type,
            clickEvent: image.clickEvent
          });
          const normalizedClickEvent = image.clickEvent;
          console.log(`TemplateEditor - 第${i + 1}张图片点击事件数据:`, normalizedClickEvent);
          // 检查是否设置了点击事件类型
          if (!normalizedClickEvent.actionType) {
            console.log(`TemplateEditor - 第${i + 1}张图片点击事件校验失败：未设置点击事件`);
            ElMessage.warning(`第${i + 1}张图片未设置点击事件`);

            // 设置校验失败标志，防止重新初始化用户输入
            window._isEcommerceValidationFailed = true;
            setTimeout(() => {
              window._isEcommerceValidationFailed = false;
            }, 1000); // 1秒后清除标志

            showEcommerceSettings.value = true;
            selectedContent.value = ecommerceSettingsContent.value;
            return;
          }
          
          // 使用统一的校验方法
          const imageClickEventResult = validateClickEvent(normalizedClickEvent);
          if (!imageClickEventResult.valid) {
            console.log(`TemplateEditor - 第${i + 1}张图片点击事件校验失败：`, imageClickEventResult.message);
            ElMessage.warning(`第${i + 1}张图片点击事件：${imageClickEventResult.message}`);
            
            // 设置校验失败标志，防止重新初始化用户输入
            window._isEcommerceValidationFailed = true;
            setTimeout(() => {
              window._isEcommerceValidationFailed = false;
            }, 1000); // 1秒后清除标志
            
            showEcommerceSettings.value = true;
            selectedContent.value = ecommerceSettingsContent.value;
            return;
          }
          
          console.log(`TemplateEditor - 第${i + 1}张图片点击事件校验通过`);
        }
      }


      console.log('TemplateEditor - 所有图片点击事件校验通过，开始校验按钮');

      // 校验按钮
      if (!ecommerceDisplayData.buttons || ecommerceDisplayData.buttons.length === 0) {
        console.log('TemplateEditor - 按钮校验失败：没有按钮数据');
        ElMessage.warning('请编辑按钮');
        
        // 设置校验失败标志，防止重新初始化用户输入
        window._isEcommerceValidationFailed = true;
        setTimeout(() => {
          window._isEcommerceValidationFailed = false;
        }, 1000); // 1秒后清除标志
        
        showEcommerceSettings.value = true;
        selectedContent.value = ecommerceSettingsContent.value;
        return;
      }
      
      console.log('TemplateEditor - 开始校验按钮点击事件，按钮数量:', ecommerceDisplayData.buttons.length);
      
      // 校验按钮点击事件
      for (let i = 0; i < ecommerceDisplayData.buttons.length; i++) {
        const button = ecommerceDisplayData.buttons[i];
        console.log(`TemplateEditor - 校验第${i + 1}个按钮:`, {
          text: button.text,
          hidden: button.hidden,
          hasClickEvent: !!button.clickEvent,
          clickEventType: button.clickEvent?.type,
          clickEvent: button.clickEvent
        });
        
        if (button.hidden) {
          console.log(`TemplateEditor - 第${i + 1}个按钮被隐藏，跳过校验`);
          continue; // 跳过隐藏的按钮
        }
        const normalizedClickEvent = button.clickEvent;
        console.log(`TemplateEditor - 第${i + 1}个按钮点击事件数据:`, normalizedClickEvent);
        if (!normalizedClickEvent.actionType) {
          console.log(`TemplateEditor - 第${i + 1}个按钮点击事件校验失败：未设置点击事件`);
          ElMessage.warning(`第${i + 1}个按钮未设置点击事件`);
          
          // 设置校验失败标志，防止重新初始化用户输入
          window._isEcommerceValidationFailed = true;
          setTimeout(() => {
            window._isEcommerceValidationFailed = false;
          }, 1000); // 1秒后清除标志
          
          ecommerceSettingsContent.value.currentData.currentButtonIndex = i;
          showEcommerceSettings.value = true;
          selectedContent.value = ecommerceSettingsContent.value;
          return;
        }
        // 使用统一的校验方法
        const buttonClickEventResult = validateClickEvent(normalizedClickEvent);
        if (!buttonClickEventResult.valid) {
          console.log(`TemplateEditor - 第${i + 1}个按钮点击事件校验失败：`, 
          buttonClickEventResult.message);
          ElMessage.warning(`第${i + 1}个按钮点击事件：${buttonClickEventResult.message}`);
          
          // 设置校验失败标志，防止重新初始化用户输入
          window._isEcommerceValidationFailed = true;
          setTimeout(() => {
            window._isEcommerceValidationFailed = false;
          }, 1000); // 1秒后清除标志
          
          ecommerceSettingsContent.value.currentData.currentButtonIndex = i;
          showEcommerceSettings.value = true;
          selectedContent.value = ecommerceSettingsContent.value;
          return;
        }
        
        console.log(`TemplateEditor - 第${i + 1}个按钮点击事件校验通过`);
      }
      
      console.log('TemplateEditor - 电商模板校验通过');
      // 电商模板校验通过，直接跳到显示预览提交弹窗
      previewSubmitDialogVisible.value = true;
      return;
    }
    
    // 长文本模板特殊校验
    if (isLongTextTemplate.value) {
      console.log('长文本模板校验，当前样式:', longTextSettings.selectedStyle);
      
      // 使用长文本模板处理器进行校验
      const templateType = templateFactory.detectTemplateType(selectedTemplate.value);
      const templateHandler = templateFactory.getTemplateHandler(templateType);
      
      if (templateHandler && templateHandler.validateContents) {
        const validationResult = templateHandler.validateContents(selectedTemplateContents.value, longTextSettings);
        
        if (!validationResult.isValid) {
          console.log('长文本模板校验失败:', validationResult.errors);
          ElMessage.warning(validationResult.errors[0] || '长文本模板校验失败');
          
          // 验证失败后跳转到对应内容
          if (validationResult.focusType && validationResult.focusIndex >= 0) {
            let targetContent = null;
            
            if (validationResult.focusType === 'text') {
              targetContent = selectedTemplateContents.value.find(content => 
                content.type === 'text' && 
                (content.isTextTitle === 1 || content.positionNumber === 2)
              );
            } else if (validationResult.focusType === 'button') {
              // 根据focusIndex选择具体的按钮
              const buttonContents = selectedTemplateContents.value.filter(content => content.type === 'button');
              if (validationResult.focusIndex < buttonContents.length) {
                targetContent = buttonContents[validationResult.focusIndex];
                console.log('长文本按钮验证失败，选择按钮:', {
                  focusIndex: validationResult.focusIndex,
                  targetButton: targetContent,
                  allButtons: buttonContents
                });
              }
            } else if (validationResult.focusType === 'image') {
              targetContent = selectedTemplateContents.value.find(content => content.type === 'image');
            }
            
            if (targetContent) {
              console.log('长文本验证失败，跳转到目标内容:', validationResult.focusType, targetContent);
              selectedContent.value = targetContent;
              
              // 根据验证失败的类型显示对应的设置面板
              nextTick(() => {
                if (validationResult.focusType === 'image') {
                  // 图片验证失败，显示常规设置面板
                  showSettings.value = true;
                  showLongTextSettings.value = false;
                  showNotificationSettings.value = false;
                  showMultiTextSettings.value = false;
                  console.log('长文本图片验证失败，显示常规设置面板');
                } else if (validationResult.focusType === 'button') {
                  // 按钮验证失败，显示常规设置面板
                  showSettings.value = true;
                  showLongTextSettings.value = false;
                  showNotificationSettings.value = false;
                  showMultiTextSettings.value = false;
                  console.log('长文本按钮验证失败，显示常规设置面板');
                } else if (validationResult.focusType === 'text') {
                  // 文本验证失败，显示常规设置面板
                  showSettings.value = true;
                  showLongTextSettings.value = false;
                  showNotificationSettings.value = false;
                  showMultiTextSettings.value = false;
                  console.log('长文本文本验证失败，显示常规设置面板');
                }
                
                console.log('长文本验证失败后面板状态:', {
                  showSettings: showSettings.value,
                  showLongTextSettings: showLongTextSettings.value,
                  selectedContent: selectedContent.value?.type,
                  focusType: validationResult.focusType
                });
              });
            }
          }
          
          // 长文本模板验证失败时直接返回，不再继续执行后续验证
          return;
        }
      }
      
      // 确保一般样式有两个按钮
      if (longTextSettings.selectedStyle === 'general') {
        const buttonContents = selectedTemplateContents.value.filter(content => content.type === 'button');
        if (buttonContents.length < 2) {
          console.log('长文本一般样式缺少第二个按钮，自动添加');
          // 使用模板处理器确保有两个按钮
          const updatedContents = templateFactory.ensureButtonsForGeneralStyle(selectedTemplateContents.value, longTextSettings);
          selectedTemplateContents.value = updatedContents;
        }
      }
    }
    
    // 多图文模板特殊校验：检查图文对数量
    if (isMultiTextTemplate.value) {
      const pairCount = multiTextSettings.pairCount || 1;
      console.log('多图文模板校验，选择的图文对数量:', pairCount);
      
      // 第一步：校验大图（头部图片）
      const headerImage = selectedTemplateContents.value.find(content => 
        content.type === 'image' && content.positionNumber === 1
      );
      
      console.log('多图文模板校验头部大图:', headerImage);
      
      // 检查大图是否已上传
      if (!headerImage || !headerImage.src || 
          headerImage.src.includes('/aim_files/aim_defult/') ||
          headerImage.src.includes('defaultImg.jpg')) {
        ElMessage.warning('请先选择图片');
        if (headerImage) {
          selectedContent.value = headerImage;
        }
        return;
      }
      
      // 检查大图的点击事件
      const headerImageResult = validateClickEvent(headerImage);
      if (!headerImageResult.valid) {
        // 显示错误提示
        ElMessage.warning(headerImageResult.message || '请设置大图点击事件');
        
        selectedContent.value = headerImage;
        nextTick(() => {
          showSettings.value = true;
          // showImageSettings.value = true;
          showButtonSettings.value = false;
          showLongTextSettings.value = false;
          showNotificationSettings.value = false;
          showMultiTextSettings.value = false;
          console.log('多图文大图点击事件验证失败，强制显示图片设置面板');
        });
        return;
      }
      
      // 第二步：校验头部文本（positionNumber: 2）
      const headerText = selectedTemplateContents.value.find(content => 
        content.type === 'text' && content.positionNumber === 2 && content.isTextTitle === 1
      );
      
      console.log('多图文模板校验头部文本:', headerText);
      
      // 检查头部文本是否存在且不为空
      if (!headerText || !headerText.content || headerText.content.trim() === '') {
        ElMessage.warning('请编辑大图上的文本内容');
        if (headerText) {
          selectedContent.value = headerText;
        }
        return;
      }
      
      // 第三步：校验小图（图文对图片）
      for (let i = 1; i <= pairCount; i++) {
        const imagePosition = (i - 1) * 2 + 3; // 第1对：位置3，第2对：位置5，第3对：位置7
        const textPosition = (i - 1) * 2 + 4;  // 第1对：位置4，第2对：位置6，第3对：位置8
        
        // 查找对应位置的图片和文本
        const pairImage = selectedTemplateContents.value.find(content => 
          content.type === 'image' && content.positionNumber === imagePosition
        );
        const pairText = selectedTemplateContents.value.find(content => 
          content.type === 'text' && content.positionNumber === textPosition
        );
        
        console.log(`多图文模板校验第${i}对:`, {
          图片位置: imagePosition,
          文本位置: textPosition,
          图片: pairImage,
          文本: pairText
        });
        
        // 如果这一对没有图片，提示用户
        if (!pairImage || !pairImage.src || 
            pairImage.src.includes('/aim_files/aim_defult/') ||
            pairImage.src.includes('defaultImg.jpg')) {
          ElMessage.warning(`请选择第${i}对的图片`);
          // 如果图片存在但是默认图片，选中它
          if (pairImage) {
            selectedContent.value = pairImage;
          }
          return;
        }
        
        // 检查小图的点击事件
        const pairImageResult = validateClickEvent(pairImage);
        if (!pairImageResult.valid) {
          // 显示错误提示
          ElMessage.warning(pairImageResult.message || `请设置第${i}对图片的点击事件`);
          
          selectedContent.value = pairImage;
          nextTick(() => {
            showSettings.value = true;
            // showImageSettings.value = true;
            showButtonSettings.value = false;
            showLongTextSettings.value = false;
            showNotificationSettings.value = false;
            showMultiTextSettings.value = false;
            console.log('多图文小图点击事件验证失败，强制显示图片设置面板');
          });
          return;
        }
      }
      
      console.log('多图文模板图片校验通过，继续后续校验');
    }
    

    // 横滑模板特殊校验
    if (isHorizontalSwipeTemplate.value) {
      // 校验前强制保存当前横滑设置面板内容，防止内容丢失
      if (selectedContent.value && selectedContent.value.type === 'horizontalSwipeSettings') {
        saveContentSettings(selectedContent.value);
      }

      // 显示横滑设置面板
      function showHorizontalSwipePanel(cardIndex) {
        const horizontalSwipeSettings = {
          type: 'horizontalSwipeSettings',
          contentId: 'horizontalswipe-settings',
          content: '横滑设置',
          isHorizontalSwipeSettings: true,
          selectedCardIndex: cardIndex
        };
        selectedContent.value = horizontalSwipeSettings;
        showHorizontalSwipeSettings.value = true;
      }
      // 从模板的pages数据中获取横滑卡片
      let pages = selectedTemplate.value.pages;
      if (typeof pages === 'string') {
        try {
          pages = JSON.parse(pages);
        } catch (e) {
          console.error('横滑模板校验 - 解析pages失败:', e);
          ElMessage.error('横滑模板数据格式错误');
          return;
        }
      }
      
      if (!pages || pages.length === 0) {
        ElMessage.warning('横滑模板没有卡片数据');
        return;
      }
      
      // 逐个卡片完整校验：每张卡片的所有内容都校验通过后才校验下一张卡片
      for (let cardIndex = 0; cardIndex < pages.length; cardIndex++) {
        const page = pages[cardIndex];
        const pageContents = page.contents || page.content || [];
        
        console.log(`横滑模板校验第${cardIndex + 1}张卡片:`, page);
        
        // 1. 校验图片是否已上传
        const imageContent = pageContents.find(content => content.type === 'image');
        
        if (!imageContent || !imageContent.src || 
            imageContent.src.includes('/aim_files/aim_defult/') ||
            imageContent.src.includes('defaultImg.jpg')) {
          ElMessage.warning(`第${cardIndex + 1}张横滑卡片图片未上传，请选择图片`);
          showHorizontalSwipePanel(cardIndex);
          return;
        }
        
        // 2. 校验标题内容
        const titleContent = pageContents.find(content => 
          content.type === 'text' && (content.isTextTitle === 1 || content.positionNumber === 2)
        );
        
        if (!titleContent || !titleContent.content || titleContent.content.trim() === '') {
          ElMessage.warning(`第${cardIndex + 1}张横滑卡片标题不能为空`);
          showHorizontalSwipePanel(cardIndex);
          return;
        }
        if (titleContent.content.length > 13) {
          ElMessage.warning(`第${cardIndex + 1}张横滑卡片标题不能超过13个字`);
          showHorizontalSwipePanel(cardIndex);
          return;
        }
        // 3. 校验文本内容
        const textContent = pageContents.find(content => 
          content.type === 'text' && content.isTextTitle !== 1 && content.positionNumber === 3
        );
        
        if (!textContent || !textContent.content || textContent.content.trim() === '') {
          ElMessage.warning(`第${cardIndex + 1}张横滑卡片内容不能为空`);
          showHorizontalSwipePanel(cardIndex);
          return;
        }
        if (textContent.content.length > 30) {
          ElMessage.warning(`第${cardIndex + 1}张横滑卡片内容不能超过30个字`);
          showHorizontalSwipePanel(cardIndex);
          return;
        }
        // 4. 校验按钮内容（如果有按钮）
        const buttonContent = pageContents.find(content => content.type === 'button');
        
        if (buttonContent) {
          const isEmpty = !buttonContent.content?.trim();
          const isDefault = buttonContent.content === '编辑按钮';
          const isDefaultVariant = buttonContent.content.includes('编辑按钮文本') || buttonContent.content.includes('默认按钮');
          
          if (isEmpty || isDefault || isDefaultVariant) {
            ElMessage.warning(`请编辑第${cardIndex + 1}张横滑卡片的按钮名称`);
            showHorizontalSwipePanel(cardIndex);
            return;
          }
          if (buttonContent.content.length > 9) {
            ElMessage.warning(`第${cardIndex + 1}张横滑卡片按钮名称不能超过9个字`);
            showHorizontalSwipePanel(cardIndex);
            return;
          }
          console.log(`第${cardIndex + 1}张卡片按钮文本校验通过，继续检查点击事件`);
          
          // 校验按钮点击事件 - 横滑模板特殊处理
          let clickEventData = page.clickEvent || {};
          if (!clickEventData.type && buttonContent.actionType) {
            clickEventData = {
              ...clickEventData,
              type: buttonContent.actionType,
              actionType: buttonContent.actionType,
              actionUrl: buttonContent.actionUrl,
            };
          }
          const result = ClickEventValidator.validate(clickEventData);
          if (!result.valid) {
            ElMessage.warning(`第${cardIndex + 1}张横滑卡片按钮：${result.message}`);
            showHorizontalSwipePanel(cardIndex);
              return;
          }
        }
        
        console.log(`横滑模板第${cardIndex + 1}张卡片校验通过`);
      }
      
      console.log('横滑模板所有卡片校验通过，继续后续校验');
    }
    
    // 多商品模板特殊校验
    if (isMultiProductTemplate.value) {
      try {
        console.log('多商品模板校验开始');
        
        // 优先使用多商品设置状态数据进行验证，而不是重新从模板内容中提取
        let multiProductData;
        if (multiProductSettingsState.value && multiProductSettingsState.value.products) {
          console.log('多商品模板校验 - 使用设置状态数据:', multiProductSettingsState.value);
          multiProductData = multiProductSettingsState.value;
        } else {
          console.log('多商品模板校验 - 从模板内容中提取数据');
          multiProductData = extractMultiProductDataFromContents(selectedTemplateContents.value);
        }
        
        // 检查数据是否有效
        if (!multiProductData || !multiProductData.products || !Array.isArray(multiProductData.products)) {
          console.error('多商品数据无效:', multiProductData);
          ElMessage.error('多商品模板数据无效，请重新设置');
          showMultiProductSettings.value = true;
          selectedContent.value = multiProductSettingsContent.value;
          console.log('selectedContent.value 赋值 products:', multiProductSettingsContent.value.currentData.products);
          return;
        }
        
        // 校验标题和描述是否为空
        if (!multiProductData.headerTitle || multiProductData.headerTitle.trim() === '') {
          ElMessage.warning('多商品标题不能为空');
          showMultiProductSettings.value = true;
          selectedContent.value = multiProductSettingsContent.value;
          console.log('selectedContent.value 赋值 products:', multiProductSettingsContent.value.currentData.products);
          return;
        }
        
        // 获取可见商品数量
        const visibleProductCount = multiProductData.productCount;
        const visibleProducts = multiProductData.products.filter(product => !product.hidden);
        
        // 校验每个可见商品
        for (let i = 0; i < Math.min(visibleProducts.length, visibleProductCount); i++) {
          const product = visibleProducts[i];
          const actualIndex = multiProductData.products.indexOf(product);
          
          // 校验商品标题
          if (!product.title || product.title.trim() === '') {
            ElMessage.warning(`第${i + 1}个商品的标题不能为空`);
            showMultiProductSettings.value = true;
            selectedContent.value = {
              ...multiProductSettingsContent.value,
              currentData: {
                ...multiProductSettingsContent.value.currentData,
                selectedProductIndex: actualIndex
              }
            };
            return;
          }
          
          // 校验商品价格
          if (!product.price || product.price.trim() === '') {
            ElMessage.warning(`第${i + 1}个商品的价格不能为空`);
            showMultiProductSettings.value = true;
            selectedContent.value = {
              ...multiProductSettingsContent.value,
              currentData: {
                ...multiProductSettingsContent.value.currentData,
                selectedProductIndex: actualIndex
              }
            };
            return;
          }
          
          // 校验图片
          if (!product.image || 
              product.image.includes('/aim_files/aim_defult/') ||
              product.image.includes('defaultImg.jpg')) {
            ElMessage.warning(`第${i + 1}个商品图片未上传，请选择图片`);
            showMultiProductSettings.value = true;
            selectedContent.value = {
              ...multiProductSettingsContent.value,
              currentData: {
                ...multiProductSettingsContent.value.currentData,
                selectedProductIndex: actualIndex
              }
            };
            return;
          }
          
          // 校验按钮点击事件
          if (!product.buttonClickEvent || !product.buttonClickEvent.type || product.buttonClickEvent.type === 'none') {
            ElMessage.warning(`请设置第${i + 1}个商品的按钮点击事件`);
            showMultiProductSettings.value = true;
            selectedContent.value = {
              ...multiProductSettingsContent.value,
              currentData: {
                ...multiProductSettingsContent.value.currentData,
                selectedProductIndex: actualIndex
              }
            };
            return;
          }

          // 使用统一的校验方法 - 需要转换数据格式
          const clickEventForValidation = {
            actionType: product.buttonClickEvent.actionType || product.buttonClickEvent.type,
            actionUrl: product.buttonClickEvent.actionUrl || product.buttonClickEvent.url || product.buttonClickEvent.phone || '',
            actionPath: product.buttonClickEvent.actionPath || product.buttonClickEvent.smsBody || product.buttonClickEvent.text || '',
            // 邮箱相关字段
            emailAddress: product.buttonClickEvent.emailAddress || '',
            emailSubject: product.buttonClickEvent.emailSubject || '',
            emailBody: product.buttonClickEvent.emailBody || '',
            // 日程相关字段
            scheduleTitle: product.buttonClickEvent.scheduleTitle || '',
            scheduleContent: product.buttonClickEvent.scheduleContent || '',
            scheduleStartTimeString: product.buttonClickEvent.scheduleStartTimeString || '',
            scheduleEndTimeString: product.buttonClickEvent.scheduleEndTimeString || '',
            // 弹窗相关字段
            popupTitle: product.buttonClickEvent.popupTitle || '',
            popupContent: product.buttonClickEvent.popupContent || '',
            popupButtonText: product.buttonClickEvent.popupButtonText || '',
            // 复制参数相关字段
            copyType: product.buttonClickEvent.copyType || '1',
            selectedParamId: product.buttonClickEvent.selectedParamId || '',
            fixedContent: product.buttonClickEvent.fixedContent || '',
            // 其他可能需要的字段
            packageName: product.buttonClickEvent.packageName || [],
            floorType: product.buttonClickEvent.floorType || '0'
          };
          console.log(`TemplateEditor - 第${i + 1}个商品按钮点击事件校验数据:`, clickEventForValidation);
          const productClickEventResult = validateClickEvent(clickEventForValidation);
          if (!productClickEventResult.valid) {
            console.log(`TemplateEditor - 第${i + 1}个商品按钮点击事件校验失败：`, productClickEventResult.message);
            ElMessage.warning(`第${i + 1}个商品按钮点击事件：${productClickEventResult.message}`);

            showMultiProductSettings.value = true;
            selectedContent.value = {
              ...multiProductSettingsContent.value,
              currentData: {
                ...multiProductSettingsContent.value.currentData,
                selectedProductIndex: actualIndex
              }
            };
            return;
          }

          console.log(`TemplateEditor - 第${i + 1}个商品按钮点击事件校验通过`);
        }
        
        console.log('多商品模板校验通过');
        // 多商品模板验证通过后，直接跳转到提交，不进行通用按钮验证
        // 因为多商品的按钮验证已在多商品专用验证中完成
        console.log('多商品模板验证完成，跳过通用验证，直接显示提交弹窗');
        previewSubmitDialogVisible.value = true;
        return;
        
      } catch (error) {
        console.error('多商品模板校验出错:', error);
        ElMessage.error('多商品模板校验出错，请检查数据是否完整');
        return;
      }
    }

    // 单卡券模板特殊校验
    if (isCardVoucherTemplate.value) {
      console.log('TemplateEditor - 单卡券模板校验开始');
      console.log('TemplateEditor - 当前模板内容:', selectedTemplateContents.value);
      // 获取当前模板内容
      const contents = selectedTemplateContents.value;
      
       // 1. 校验输入框内容（满减金额、优惠金额等）
      // 检查必填的文本内容
      const requiredTexts = [
        { positionNumber: 1, name: '优惠金额' },
        { positionNumber: 3, name: '使用条件' },
        { positionNumber: 4, name: '券名称' },  // 添加券名称校验
        { positionNumber: 5, name: '有效期' },
      ];
      
      for (const requiredText of requiredTexts) {
        const textContent = contents.find(content => 
          content.type === 'text' && content.positionNumber === requiredText.positionNumber
        );
        
        console.log(`校验${requiredText.name}:`, textContent);
        
        // 更严格的空值校验
        if (!textContent || 
            !textContent.content || 
            textContent.content.trim() === '' ||
            textContent.content === '请输入' ||
            textContent.content === '请设置' ||
            textContent.content.includes('默认')) {
          ElMessage.warning(`请填写${requiredText.name}`);
          showCardVoucherSettings.value = true;
          selectedContent.value = cardVoucherSettingsContent.value;
          return;
        }
      }
      
       // 2. 校验logo图片
      const logoContent = contents.find(content => 
        content.type === 'image' && content.positionNumber === 6
      );
      
      if (!logoContent || !logoContent.src || 
          logoContent.src.includes('/aim_files/aim_defult/') ||
          logoContent.src.includes('defaultImg.jpg')) {
        ElMessage.warning('请上传logo图片');
        showCardVoucherSettings.value = true;
        selectedContent.value = cardVoucherSettingsContent.value;
        return;
      }
      
      // 3. 强制校验logo图片点击事件（无论是否有点击事件配置都校验）
      const logoClickEventData = extractClickEventValidationFields(logoContent);
      const logoResult = validateClickEvent(logoClickEventData);
      if (!logoResult.valid) {
        ElMessage.warning('logo图片：' + logoResult.message);
        showCardVoucherSettings.value = true;
        selectedContent.value = cardVoucherSettingsContent.value;
        return;
      }

      // 4. 校验按钮文本
      const buttonContent = contents.find(content => 
        content.type === 'button' && content.positionNumber === 7
      );

      if (!buttonContent || !buttonContent.content || buttonContent.content.trim() === '') {
        ElMessage.warning('请设置按钮文本');
        showCardVoucherSettings.value = true;
        selectedContent.value = cardVoucherSettingsContent.value;
        return;
      }
      
      // 5. 校验按钮点击事件
    console.log('Button完整内容:', buttonContent);
    console.log('Button点击事件数据:', buttonContent.clickEvent);
      console.log('Button action数据:', {
        type: buttonContent.actionType,
        url: buttonContent.actionUrl
      });
      // 组装通用校验对象，简化为只传主要字段
      const buttonClickEventData = extractClickEventValidationFields(buttonContent);
      console.log('Button完整内容:', buttonContent);
      console.log('Button合并后的点击事件数据:', buttonClickEventData);

      const buttonResult = validateClickEvent(buttonClickEventData);
      console.log('Button校验结果:', buttonResult);

      if (!buttonResult.valid) {
        ElMessage.warning('按钮：' + buttonResult.message);
        showCardVoucherSettings.value = true;
        selectedContent.value = cardVoucherSettingsContent.value;
        return;
      }
      console.log('TemplateEditor - 单卡券模板校验通过');
      previewSubmitDialogVisible.value = true;
      return;
    }

    // 券+商品模板特殊校验
    if (isCouponProductTemplate.value) {
      console.log('TemplateEditor - 券+商品模板校验开始');
      
      // 检查券+商品设置状态是否初始化
      if (!couponProductSettingsState.value) {
        ElMessage.warning('券商品设置未初始化，请先配置券商品内容');
        showCouponProductSettings.value = true; // 显示券设置面板
        selectedContent.value = couponProductSettingsContent.value;
        return;
      }
      
      // 获取券+商品显示数据进行校验
      const displayData = couponProductDisplayData.value;
      console.log('TemplateEditor - 券+商品校验数据:', displayData);
      
      if (!displayData) {
        ElMessage.warning('券商品数据未加载');
        showCouponProductSettings.value = true;
        selectedContent.value = couponProductSettingsContent.value;
        return;
      }
      
      // 从上往下校验：1. 先校验头部商品图片
      if (displayData.headerProduct) {
        const headerProduct = displayData.headerProduct;
        
        // 校验头部商品图片是否为默认图片
        const isDefaultHeaderImage = !headerProduct.image ||
          headerProduct.image.trim() === '' ||
          headerProduct.image.includes('/aim_files/aim_defult/') ||
          headerProduct.image.includes('defaultImg') ||
          headerProduct.image.includes('defaultImg2.jpg');

        // 复制模式特殊处理：只有确实是默认图片才校验
        const isCopyMode = window._isCopyMode || false;
        const shouldValidateHeaderImage = isCopyMode ?
          (isDefaultHeaderImage && (
            !headerProduct.image ||
            headerProduct.image.includes('/aim_files/aim_defult/') ||
            headerProduct.image.includes('defaultImg')
          )) :
          isDefaultHeaderImage;

        // 如果是默认图片
        if (shouldValidateHeaderImage) {
          console.log('TemplateEditor - 头部商品图片校验失败：图片为默认图片');
        ElMessage.warning('请上传logo图片');
          showCouponProductSettings.value = true;
          selectedContent.value = couponProductSettingsContent.value;
          return;
        }
        if (headerProduct.imageClickEvent) {
          console.log('校验头部图片点击事件数据:', headerProduct.imageClickEvent);
          const headerClickEventResult = ClickEventValidator.validate(headerProduct.imageClickEvent);
          if (!headerClickEventResult.valid) {
            ElMessage.warning(`logo图片点击事件：${headerClickEventResult.message}`);
            showCouponProductSettings.value = true;
            selectedContent.value = couponProductSettingsContent.value;
            return;
          }
        } else {
          ElMessage.warning('请设置logo图片点击事件');
          showCouponProductSettings.value = true;
          selectedContent.value = couponProductSettingsContent.value;
          return;
        }
        
      }
      
      // 2. 校验券按钮点击事件
      if (displayData.coupon) {
        const coupon = displayData.coupon;
        // 校验券按钮点击事件
        if (coupon.buttonClickEvent) {
          const couponClickEventResult = ClickEventValidator.validate(coupon.buttonClickEvent);
          if (!couponClickEventResult.valid) {
            ElMessage.warning(`券点击事件：${couponClickEventResult.message}`);
            showCouponProductSettings.value = true;
            selectedContent.value = couponProductSettingsContent.value;
            return;
          }
        }
      }
      
      // 3. 逐个校验商品（从上往下）
      for (let i = 0; i < displayData.products.length; i++) {
        const product = displayData.products[i];
        
        // 跳过隐藏的商品
        if (product.hidden) continue;
        
        // 校验商品图片
        const isDefaultProductImage = !product.image ||
          product.image.trim() === '' ||
          product.image.includes('/aim_files/aim_defult/') ||
          product.image.includes('defaultImg2');

        // 复制模式特殊处理：只有确实是默认图片才校验
        const isCopyMode = window._isCopyMode || false;
        const shouldValidateProductImage = isCopyMode ?
          (isDefaultProductImage && (
            !product.image ||
            product.image.includes('/aim_files/aim_defult/') ||
            product.image.includes('defaultImg')
          )) :
          isDefaultProductImage;

        if (shouldValidateProductImage) {
          console.log(`TemplateEditor - 第${i + 1}个商品图片校验失败：图片为默认图片`);
          ElMessage.warning(`第${i + 1}个商品图片为默认图片，请上传图片`);
          couponProductSettingsState.value.selectedProductIndex = i;
          showCouponProductSettings.value = true;
          selectedContent.value = couponProductSettingsContent.value;
          return;
        }
        
        // 校验商品按钮点击事件
        if (product.buttonClickEvent) {
          const productClickEventResult = ClickEventValidator.validate(product.buttonClickEvent);
          if (!productClickEventResult.valid) {
            ElMessage.warning(`商品点击事件：${productClickEventResult.message}`);
            showCouponProductSettings.value = true;
            selectedContent.value = couponProductSettingsContent.value;
            return;
          }
          
          
        }
      }
      
      console.log('TemplateEditor - 券+商品模板校验通过');
      // 券+商品模板校验通过，直接跳到显示预览提交弹窗
      previewSubmitDialogVisible.value = true;
      return;
    }

    // console.log('开始图片校验流程');
    
    // 2. 第二步：先检查图片相关内容
    const imageContents = selectedTemplateContents.value.filter(content => 
      content.type === 'image' || content.type === 'background' || content.type === 'video'
    );
    
    // console.log('检查图片内容，找到图片数量:', imageContents.length);
    
    // 检查是否是轮播图模板，如果是则使用特殊的轮播图校验
    const isCarouselTemplate = selectedTemplate.value && 
      templateFactory.isCarouselTemplate(selectedTemplate.value);
    
    if (isCarouselTemplate) {
      console.log('检测到轮播图模板，开始特殊校验流程...');
      
      // 查找轮播图内容
      let carouselContent = imageContents.find(content => 
        content.isCarousel || content.carouselImages
      );
      
      // 如果没有找到轮播图内容，但有普通图片内容，将其转换为轮播图格式
      if (!carouselContent && imageContents.length > 0) {
        const imageContent = imageContents[0];
        console.log('将普通图片内容转换为轮播图格式:', imageContent);
        
        carouselContent = {
          ...imageContent,
          isCarousel: true,
          carouselImages: [
            {
              src: imageContent.src || '/aim_files/aim_defult/defaultImg.jpg',
              alt: '图片1',
              positionNumber: 5,
              clickEvent: {
                type: 'browser',
                url: '',
                phone: '',
                text: '',
                app: '',
                packageName: '',
                floorType: '0',
                quick: '',
                email: '',
                emailSubject: '',
                emailBody: '',
                schedule: '',
                scheduleStartTimeString: '',
                scheduleEndTimeString: '',
                popup: '',
                popupContent: '',
                popupButtonText: ''
              }
            }
          ],
          currentImageIndex: 0
        };
        
        // 更新内容数组中的图片内容
        const contentIndex = selectedTemplateContents.value.findIndex(content => 
          content.type === 'image' || content.type === 'background' || content.type === 'video'
        );
        
        if (contentIndex !== -1) {
          selectedTemplateContents.value[contentIndex] = carouselContent;
          console.log('已更新轮播图内容到模板内容数组');
        }
      }
      
      if (carouselContent) {
        // 不在开始时就设置面板状态，只在校验失败时才设置
        // 等待DOM更新
        await nextTick();
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 轮播图校验：第一步 - 优先校验所有图片是否已上传
        let carouselValidationResult = true;
        
        try {
          if (carouselContent.carouselImages && Array.isArray(carouselContent.carouselImages)) {
            console.log('开始轮播图校验 - 第一步：检查图片上传状态');
            
            // 第一轮：检查所有图片是否已上传
            for (let i = 0; i < carouselContent.carouselImages.length; i++) {
              const image = carouselContent.carouselImages[i];
              
              console.log(`校验第${i + 1}张轮播图片上传状态:`, image);
              
              // 检查图片是否有有效的src
              // 普通轮播图模板要求用户上传真实图片，不接受任何默认图片
              // 包括defaultImg.jpg、defaultImg48_65.jpg等都不被接受
              if (!image.src ||
                  image.src === '/aim_files/aim_defult/defaultImg.jpg' ||
                  image.src.includes('/aim_files/aim_defult/') ||
                  image.src.includes('defaultImg')) {
                ElMessage.warning(`第${i + 1}张轮播图片未选择，请上传图片`);
                carouselValidationResult = false;

                // 只在校验失败时设置面板状态
                selectedContent.value = carouselContent;
                nextTick(() => {
                  showSettings.value = true;
                  showButtonSettings.value = false;
                  showLongTextSettings.value = false;
                  showNotificationSettings.value = false;
                  showMultiTextSettings.value = false;
                });

                // 切换到当前图片显示错误
                const updatedContent = {
                  ...carouselContent,
                  currentImageIndex: i,
                  _validationError: true
                };
                emit('update:content', updatedContent);

                return; // 立即返回，不继续后续校验
              }
            }
            
            console.log('轮播图图片上传校验通过 - 第二步：检查点击事件配置');
            
            // 第二轮：检查所有图片的点击事件配置
            for (let i = 0; i < carouselContent.carouselImages.length; i++) {
              const image = carouselContent.carouselImages[i];
              
              console.log(`校验第${i + 1}张轮播图片点击事件:`, image.clickEvent);
              
              // 切换到当前图片进行校验
              const updatedContent = {
                ...carouselContent,
                currentImageIndex: i,
                _validationError: true
              };
              emit('update:content', updatedContent);
              
              // 等待界面更新
              await nextTick();
              await new Promise(resolve => setTimeout(resolve, 100));
              
              // 检查点击事件配置 - 使用统一的校验方法
              if (image.clickEvent && image.clickEvent.type !== 'none') {
                // 将clickEvent转换为标准格式，然后使用统一的validateClickEvent方法
                const contentForValidation = ActionJsonGenerator.toClickEventSettings(
                  image.clickEvent,
                  `carousel-image-${i}`,
                  'image'
                );

                const validationResult = validateClickEvent(contentForValidation);

                if (!validationResult.valid) {
                  ElMessage.warning(`第${i + 1}张轮播图片：${validationResult.message}`);
                  carouselValidationResult = false;

                  // 只在校验失败时设置面板状态
                  selectedContent.value = carouselContent;
                  nextTick(() => {
                    showSettings.value = true;
                    showButtonSettings.value = false;
                    showLongTextSettings.value = false;
                    showNotificationSettings.value = false;
                    showMultiTextSettings.value = false;
                  });

                  // 切换到当前图片显示错误
                  const updatedContent = {
                    ...carouselContent,
                    currentImageIndex: i,
                    _validationError: true
                  };
                  emit('update:content', updatedContent);

                  return; // 立即返回
                }
              }
            }
            
            // 如果所有轮播图片都校验通过，继续后续校验
            if (carouselValidationResult) {
              console.log('轮播图校验全部通过，继续后续内容校验');
            }
          } else {
            console.log('轮播图内容没有carouselImages数组，跳过轮播图校验');
          }
        } catch (error) {
          console.error('轮播图校验过程中发生错误:', error);
          ElMessage.error('轮播图校验失败，请检查配置');
          return;
        }
      } else {
        console.log('没有找到轮播图内容，跳过轮播图校验');
      }
    } else {
      // 非轮播图模板，使用原有的图片校验逻辑
      // 遍历所有图片内容 - 确保先完成所有图片校验再校验其他内容
      for (let i = 0; i < imageContents.length; i++) {
        const imageContent = imageContents[i];
        
        // 跳过红包模板的背景图片校验 - 红包模板背景图不需要强制校验
        if (isRedPacketTemplate(selectedTemplate.value) && imageContent.type === 'background') {
          // console.log('红包模板背景图片，跳过校验');
          continue;
        }
        
        // 跳过电商模板的图片校验 - 电商模板使用电商设置面板管理图片
        if (isEcommerceTemplate.value) {
          console.log('电商模板图片，由电商设置面板管理，跳过常规校验');
          continue;
        }
        
        // 跳过横滑模板的图片校验 - 横滑模板使用横滑设置面板管理图片
        if (isHorizontalSwipeTemplate.value) {
          console.log('横滑模板图片，由横滑设置面板管理，跳过常规校验');
          continue;
        }
        
        // 跳过多图文模板已在特殊校验中处理的图片
        if (isMultiTextTemplate.value) {
          // 跳过头部大图（位置1）
          if (imageContent.positionNumber === 1) {
            console.log('多图文模板头部大图已在特殊校验中处理，跳过通用校验');
            continue;
          }
          // 跳过图文对小图（位置3,5,7等）
          if (imageContent.positionNumber >= 3 && (imageContent.positionNumber % 2 === 1)) {
            console.log('多图文模板图文对小图已在特殊校验中处理，跳过通用校验');
            continue;
          }
        }
        
        // 检查图片是否已上传 - 改进默认图片的识别（关键调整）
        if (!isEditMode.value) { 
          // console.log('新建模式，检查图片是否已上传');
          // 新建模式：严格校验默认图片
          if (imageContent.type === 'video') {
            // 视频类型的校验
            if (!imageContent.src || 
                imageContent.src.includes('defaultVideo.jpg') || 
                imageContent.src === '/aim_files/aim_defult/defaultVideo.jpg') {
              ElMessage.warning('请先选择视频');
              selectedContent.value = imageContent;
              nextTick(() => {
                showSettings.value = true;
                // showImageSettings.value = true;
                showButtonSettings.value = false;
                showLongTextSettings.value = false;
                showNotificationSettings.value = false;
                showMultiTextSettings.value = false;
              });
              return;
            }
            console.log('视频校验时的封面:', imageContent.aimCover);
            // 视频封面的校验
            if (!imageContent.aimCover || 
                imageContent.aimCover === '/aim_files/aim_defult/defaultVideo.jpg' ||
                imageContent.aimCover.includes('视频位建议') ||
                imageContent.aimCover.includes('该图片位建议'))  {
              ElMessage.warning('请上传视频封面');
              selectedContent.value = imageContent;
              nextTick(() => {
                showSettings.value = true;
                // showImageSettings.value = true;
                showButtonSettings.value = false;
                showLongTextSettings.value = false;
                showNotificationSettings.value = false;
                showMultiTextSettings.value = false;
              });
              return;
            }
          } else {
            // 普通图片的校验 - 复制模式特殊处理
            const isDefaultImage = !imageContent.src ||
                imageContent.src.includes('/aim_files/aim_defult/') ||
                imageContent.src.includes('defaultImg.jpg') ||
                (imageContent.defaultSrc && imageContent.src === imageContent.defaultSrc);

            // 如果是复制模式，只有当图片确实是默认图片时才校验
            // 如果图片来自接口且不是默认图片，说明是用户之前上传的图片，不需要校验
            const isCopyMode = window._isCopyMode || false;
            const shouldValidateImage = isCopyMode ?
              (isDefaultImage && (
                !imageContent.src ||
                imageContent.src.includes('/aim_files/aim_defult/') ||
                imageContent.src.includes('defaultImg')
              )) :
              isDefaultImage;

            console.log('TemplateEditor - 图片校验:', {
              src: imageContent.src,
              isDefaultImage,
              isCopyMode,
              shouldValidateImage
            });

            if (shouldValidateImage) {
              ElMessage.warning('请上传图片');
              selectedContent.value = imageContent;
              nextTick(() => {
                showSettings.value = true;
                // showImageSettings.value = true;
                showButtonSettings.value = false;
                showLongTextSettings.value = false;
                showNotificationSettings.value = false;
                showMultiTextSettings.value = false;
              });
              return;
            }
          }
        } else {
          // 编辑模式：仅当用户修改过（userEdited=true）且使用默认图片时提示
          if (imageContent.userEdited) {
            if (imageContent.type === 'video') {
              // 视频类型的校验
              if (!imageContent.src || 
                  imageContent.src.includes('defaultVideo.jpg') || 
                  imageContent.src === '/aim_files/aim_defult/defaultVideo.jpg') {
                ElMessage.warning('请先选择视频');
                selectedContent.value = imageContent;
                nextTick(() => {
                  showSettings.value = true;
                  // showImageSettings.value = true;
                  showButtonSettings.value = false;
                  showLongTextSettings.value = false;
                  showNotificationSettings.value = false;
                  showMultiTextSettings.value = false;
                });
                return;
              }
              // 视频封面的校验
              if (!imageContent.aimCover || 
                  imageContent.aimCover === '/aim_files/aim_defult/defaultVideo.jpg') {
                ElMessage.warning('请上传视频封面');
                selectedContent.value = imageContent;
                nextTick(() => {
                  showSettings.value = true;
                  // showImageSettings.value = true;
                  showButtonSettings.value = false;
                  showLongTextSettings.value = false;
                  showNotificationSettings.value = false;
                  showMultiTextSettings.value = false;
                });
                return;
              }
            } else {
              // 普通图片的校验
              if (!imageContent.src || 
                  imageContent.src.includes('/aim_files/aim_defult/') ||
                  imageContent.src.includes('defaultImg.jpg') ||
                  (imageContent.defaultSrc && imageContent.defaultSrc.includes('/aim_files/aim_defult/') && imageContent.src === imageContent.defaultSrc)) {
                ElMessage.warning('请上传图片');
                selectedContent.value = imageContent;
                nextTick(() => {
                  showSettings.value = true;
                  // showImageSettings.value = true;
                  showButtonSettings.value = false;
                  showLongTextSettings.value = false;
                  showNotificationSettings.value = false;
                  showMultiTextSettings.value = false;
                });
                return;
              }
            }
          }
        }
        // 图片必须检查点击事件（原有逻辑不变）
        // 视频类型不需要检查点击事件
        if (imageContent.type !== 'video' && !isMultiProductTemplate.value){
          if (!imageContent.actionType) {
            imageContent.actionType = 'OPEN_BROWSER';
          }
          
          // 强制再次从缓存获取actionUrl
          if (window.CLICK_EVENT_CACHE && imageContent.type) {
            // 使用新的缓存键机制
            const cacheKey = getCacheKey(imageContent);
            const cachedData = window.CLICK_EVENT_CACHE[cacheKey];
            if (cachedData && cachedData.actionUrl) {
              // console.log('从缓存获取到链接地址:', cachedData.actionUrl);
              imageContent.actionUrl = cachedData.actionUrl;
            }
          }
          
          // 检查图片点击事件设置
          const clickEventResult = validateClickEvent(imageContent);
          if (!clickEventResult.valid) {
            // 显示错误提示
            ElMessage.warning(clickEventResult.message || '请设置图片点击事件');
            
            // 选中该图片内容
            selectedContent.value = imageContent;
            // 强制显示图片设置面板
            nextTick(() => {
              showSettings.value = true;
              // showImageSettings.value = true;
              showButtonSettings.value = false;
              showLongTextSettings.value = false;
              showNotificationSettings.value = false;
              showMultiTextSettings.value = false;
              console.log('图片点击事件验证失败，强制显示图片设置面板');
            });
            return;
          }
        }
      }
    }
    
    // console.log('图片校验通过，开始检查按钮内容');
    
    // 3. 第三步：所有图片校验通过后，检查按钮相关内容
    const buttonContents = selectedTemplateContents.value.filter(content => content.type === 'button');
    
    // 对于通知类模板，只校验用户设置的按钮数量
    let buttonsToValidate = buttonContents;
    if (isNotificationTemplate.value && notificationSettings) {
      const maxVisibleButtons = notificationSettings.maxVisibleButtons || 2;
      buttonsToValidate = buttonContents.slice(0, maxVisibleButtons);
      console.log('通知类模板按钮校验：用户设置按钮数量:', maxVisibleButtons, '实际校验按钮数量:', buttonsToValidate.length);
    }
    
    // 遍历需要校验的按钮内容
    for (let i = 0; i < buttonsToValidate.length; i++) {
      const buttonContent = buttonsToValidate[i];
      
      console.log('当前按钮索引:', i);
      console.log('是否为横滑模板:', isHorizontalSwipeTemplate.value);
      console.log('是否为通知类模板:', isNotificationTemplate.value);
      
      // 检查按钮文本是否为空或为默认文本
      const isEmpty = !buttonContent.content?.trim();
      const isEditButton = buttonContent.content === '编辑按钮';
      const containsDefault = buttonContent.content && buttonContent.content.includes('默认');
      
      if (isEmpty || isEditButton || containsDefault) {
        ElMessage.warning('请编辑按钮名称');
        
        // 横滑模板特殊处理：显示横滑设置面板
        if (isHorizontalSwipeTemplate.value) {
          const horizontalSwipeSettings = {
            type: 'horizontalSwipeSettings',
            contentId: 'horizontalswipe-settings',
            content: '横滑设置',
            isHorizontalSwipeSettings: true
          };
          
          selectedContent.value = horizontalSwipeSettings;
          showHorizontalSwipeSettings.value = true;
          
          return;
        }
        
        // 其他模板：设置选中内容并显示按钮设置面板
        selectedContent.value = buttonContent;
        
        // 明确控制面板状态，防止闪烁
        nextTick(() => {
          showSettings.value = true;
          showButtonSettings.value = true;
          // showImageSettings.value = false;
          showLongTextSettings.value = false;
          showNotificationSettings.value = false;
          showMultiTextSettings.value = false;
          console.log('按钮文本验证失败，显示按钮设置面板');
        });
        
        return;
      }
      
      // 按钮文本校验通过后，检查按钮点击事件
      // 横滑模板使用专用验证逻辑，跳过通用验证
      if (isHorizontalSwipeTemplate.value) {
        // 横滑模板专用按钮校验逻辑
        console.log('横滑模板开始专用按钮点击事件验证');
        
        // 从pages数据中获取所有卡片的按钮信息
        if (selectedTemplate.value && selectedTemplate.value.pages) {
          for (let pageIndex = 0; pageIndex < selectedTemplate.value.pages.length; pageIndex++) {
            const page = selectedTemplate.value.pages[pageIndex];
            if (page.cards && Array.isArray(page.cards)) {
              for (let cardIndex = 0; cardIndex < page.cards.length; cardIndex++) {
                const card = page.cards[cardIndex];
                
                // 检查按钮是否有点击事件设置
                if (card.buttonContent && card.buttonContent.trim() && 
                    card.buttonContent !== '编辑按钮' && 
                    !card.buttonContent.includes('默认')) {
                  
                  // 按钮文本已设置，检查点击事件
                  if (!card.clickEvent || !card.clickEvent.type || card.clickEvent.type === 'none') {
                    ElMessage.warning(`第${cardIndex + 1}张卡片的按钮点击事件不能为空`);
                    
                    // 显示横滑设置面板
                    const horizontalSwipeSettings = {
                      type: 'horizontalSwipeSettings',
                      contentId: 'horizontalswipe-settings',
                      content: '横滑设置',
                      isHorizontalSwipeSettings: true
                    };
                    
                    selectedContent.value = horizontalSwipeSettings;
                    showHorizontalSwipeSettings.value = true;
                    console.log(`第${cardIndex + 1}张卡片按钮点击事件验证失败，显示横滑设置面板`);
                    return;
                  }
                  
                  // 根据点击事件类型验证对应的URL
                  switch (card.clickEvent.type) {
                    case 'OPEN_BROWSER':
                    case 'OPEN_URL':
                      if (!card.clickEvent.target || !card.clickEvent.target.trim()) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮链接地址不能为空`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        console.log(`第${cardIndex + 1}张卡片按钮链接地址验证失败，显示横滑设置面板`);
                        return;
                      }
                      
                      if (!card.clickEvent.target.startsWith('https://')) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮链接地址必须以https://开头`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        console.log(`第${cardIndex + 1}张卡片按钮链接格式验证失败，显示横滑设置面板`);
                        return;
                      }
                      break;
                      
                    case 'DIAL_PHONE':
                      if (!card.clickEvent.target || !card.clickEvent.target.trim()) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮电话号码不能为空`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        return;
                      }
                      break;
                      
                    case 'COPY_PARAMETER':
                      if (!card.clickEvent.target || !card.clickEvent.target.trim()) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮复制内容不能为空`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        return;
                      }
                      break;
                      
                    case 'OPEN_APP':
                      if (!card.clickEvent.target || !card.clickEvent.target.trim()) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮APP链接不能为空`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        return;
                      }
                      break;
                      
                    case 'OPEN_QUICK':
                      if (!card.clickEvent.target || !card.clickEvent.target.trim()) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮快应用链接不能为空`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        return;
                      }
                      
                      if (!card.clickEvent.target.startsWith('hap://app/')) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮快应用链接必须以hap://app/开头`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        return;
                      }
                      break;
                      
                    case 'OPEN_SMS':
                      if (!card.clickEvent.target || !card.clickEvent.target.trim()) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮短信号码不能为空`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        return;
                      }
                      break;
                      
                    case 'OPEN_EMAIL':
                      if (!card.clickEvent.target || !card.clickEvent.target.trim()) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮邮箱地址不能为空`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        return;
                      }
                      break;
                      
                    case 'OPEN_SCHEDULE':
                      if (!card.clickEvent.target || !card.clickEvent.target.trim()) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮日程内容不能为空`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        return;
                      }
                      break;
                      
                    case 'OPEN_POPUP':
                      if (!card.clickEvent.target || !card.clickEvent.target.trim()) {
                        ElMessage.warning(`第${cardIndex + 1}张卡片的按钮弹窗内容不能为空`);
                        
                        const horizontalSwipeSettings = {
                          type: 'horizontalSwipeSettings',
                          contentId: 'horizontalswipe-settings',
                          content: '横滑设置',
                          isHorizontalSwipeSettings: true
                        };
                        
                        selectedContent.value = horizontalSwipeSettings;
                        showHorizontalSwipeSettings.value = true;
                        return;
                      }
                      break;
                  }
                }
              }
            }
          }
        }
        
        console.log('横滑模板按钮校验全部通过');
      } else {
        const buttonClickEventResult = validateClickEvent(buttonContent);
        if (!buttonClickEventResult.valid) {
          // 显示错误提示
          ElMessage.warning(buttonClickEventResult.message || '请设置按钮点击事件');
          
          // 其他模板：如果按钮校验失败，设置选中内容并显示按钮设置面板
          selectedContent.value = buttonContent;
          
          // 明确控制面板状态，防止闪烁
          nextTick(() => {
            showSettings.value = true;
            showButtonSettings.value = true;
            // showImageSettings.value = false;
            showLongTextSettings.value = false;
            showNotificationSettings.value = false;
            showMultiTextSettings.value = false;
            console.log('按钮点击事件验证失败，显示按钮设置面板');
          });
          
          return;
        }
      }
    }

    // 4. 第四步：检查标题文本内容
    const titleTextContents = selectedTemplateContents.value.filter(content => 
      content.type === 'text' && content.isTextTitle
    );
    
    // console.log('检查标题文本内容，找到标题数量:', titleTextContents.length);
    
    for (let i = 0; i < titleTextContents.length; i++) {
      const textContent = titleTextContents[i];
      // console.log('检查标题文本:', textContent.content);
      
      if (!textContent.content?.trim()) {
        // console.log('标题文本为空');
        ElMessage.warning('请填写标题文本');
        selectContent(textContent);
        return;
      }
    }
    
    // console.log('所有校验通过，准备显示预览提交弹窗');
    
    // 所有校验通过，显示预览提交弹窗
    previewSubmitDialogVisible.value = true;
  } catch (error) {
    console.error('提交模板错误:', error);
    ElMessage.error('提交模板出错，请重试');
  }
};


// 判断是否是红包模板
const isRedPacketTemplate = (template) => {
  if (!template) return false;
  
  // 使用简化的红包模板判断逻辑
  return template.cardId === 'com.hbm.redpacket' || 
         template.tplType === 'redpacket' || 
         (template.scene && template.scene.includes('红包'));
};

// 同步缓存中的数据到内容对象
const syncCacheToContents = () => {
  if (!window.CLICK_EVENT_CACHE) return;
      
  // 遍历所有内容
  selectedTemplateContents.value.forEach(content => {
    if (!content || !content.type || !content.contentId) return;
    
    // 只为需要点击事件的内容类型（图片和按钮）同步缓存
    if (content.type !== 'image' && content.type !== 'button') return;
    
    // 使用新的缓存键机制
    const cacheKey = getCacheKey(content);
    
    // 检查缓存中是否有对应的数据
    const cachedData = window.CLICK_EVENT_CACHE[cacheKey];
    if (!cachedData) return;
    
    // 记录之前的状态便于调试
    const previousActionUrl = content.actionUrl;
    const previousActionType = content.actionType;
    
    // 同步基本数据
    if (cachedData.actionType) {
      content.actionType = cachedData.actionType;
    }
    
    if (cachedData.actionUrl !== undefined) {
      content.actionUrl = cachedData.actionUrl;
    }
    
    if (cachedData.actionPath !== undefined) {
      content.actionPath = cachedData.actionPath;
    }
    
    // 同步APP相关字段
    if (cachedData.packageName !== undefined) {
      content.packageName = cachedData.packageName;
    }
    
    if (cachedData.floorType !== undefined) {
      content.floorType = cachedData.floorType;
    }
    
    // 同步邮件相关字段
    if (cachedData.emailAddress !== undefined) {
      content.emailAddress = cachedData.emailAddress;
    }
    
    if (cachedData.emailSubject !== undefined) {
      content.emailSubject = cachedData.emailSubject;
    }
    
    if (cachedData.emailBody !== undefined) {
      content.emailBody = cachedData.emailBody;
    }
    
    // 同步日程相关字段
    if (cachedData.scheduleStartTimeString !== undefined) {
      content.scheduleStartTimeString = cachedData.scheduleStartTimeString;
    }
    
    if (cachedData.scheduleEndTimeString !== undefined) {
      content.scheduleEndTimeString = cachedData.scheduleEndTimeString;
    }
    
    // 同步弹窗相关字段
    if (cachedData.popupTitle !== undefined) {
      content.popupTitle = cachedData.popupTitle;
    }
    
    if (cachedData.popupContent !== undefined) {
      content.popupContent = cachedData.popupContent;
    }
    
    if (cachedData.popupButtonText !== undefined) {
      content.popupButtonText = cachedData.popupButtonText;
    }
    
    // 记录同步情况
    if (previousActionUrl !== content.actionUrl || previousActionType !== content.actionType) {
      console.log(`同步缓存数据到内容(${cacheKey}):`, {
        actionType: content.actionType,
        actionUrl: content.actionUrl
      });
    }
  });
};

// 处理预览模板名称更新
const handlePreviewTemplateNameUpdate = (newName) => {
  templateName.value = newName;
};

// 取消提交模板
const cancelSubmitTemplate = () => {
  previewSubmitDialogVisible.value = false;
};

// 保存最后编辑的内容到本地存储
const saveLastEditContent = () => {
  // 保存编辑内容到本地存储逻辑
  // ...
};

// 继续编辑
const continueEditing = () => {
  // 继续编辑逻辑
  // ...
};

// 重新编辑
const restartEditing = () => {
  // 重新编辑逻辑
  // ...
};

// 获取模板类型
const getTemplateType = () => {
  if (selectedTemplate.value) {
    
    // 确保正确判断模板类型
    const detectedType = templateFactory.detectTemplateType(selectedTemplate.value);
    
    // 如果检测失败，尝试从DOM中获取cardId作为备选方案
    if (detectedType === 'standard') {
      const container = document.querySelector('.template-preview-container');
      if (container) {
        const domCardId = container.getAttribute('data-card-id');
        console.log('从DOM获取的cardId:', domCardId);
        
        if (domCardId === 'com.hbm.carouse') {
          return 'horizontalswipe';
        }
      }
    }
    
    return detectedType || 'standard';
  }
  return 'standard';
};

// 保存内容的设置到对应的内容对象中
const saveContentSettings = (content) => {
  try {
    if (!content) {
      console.warn('保存内容设置时接收到空内容');
      return;
    }
    
    console.log('保存内容设置，content类型:', content.type, 'contentId:', content.contentId);
    
    // 特殊处理横滑设置面板
    if (content.isHorizontalSwipeSettings || content.type === 'horizontalSwipeSettings') {
      console.log('检测到横滑设置面板，强制触发数据同步');
      
      // 对于横滑设置面板，我们需要触发一次设置的同步保存
      // 这确保了用户在设置面板中的所有输入都被保存到模板数据中
      if (showHorizontalSwipeSettings.value) {
        // 发出一个事件通知横滑设置面板立即保存所有数据
        nextTick(() => {
          console.log('通知横滑设置面板立即保存数据');
          // 通过ref直接调用设置组件的方法（如果可用）
          const horizontalSwipeSettingsComponent = document.querySelector('[data-component="horizontal-swipe-settings"]');
          if (horizontalSwipeSettingsComponent) {
            // 触发一个自定义事件
            horizontalSwipeSettingsComponent.dispatchEvent(new CustomEvent('force-save-settings'));
          }
        });
      }
      return;
    }
    
    // 查找内容在数组中的位置
    const contentIndex = selectedTemplateContents.value.findIndex(item => 
      item && item.contentId === content.contentId
    );
    
    if (contentIndex !== -1) {
      // 创建新对象，避免直接修改引用
      const updatedContent = JSON.parse(JSON.stringify(selectedTemplateContents.value[contentIndex]));
      
      // 只为图片和按钮类型保存点击事件相关的字段
      if (content.type === 'image' || content.type === 'button') {
        if (content.actionType !== undefined) {
          updatedContent.actionType = content.actionType;
        }

        if (content.actionUrl !== undefined) {
          updatedContent.actionUrl = content.actionUrl;
        }

        if (content.actionPath !== undefined) {
          updatedContent.actionPath = content.actionPath;
        }

        // 不保存额外字段，所有信息都通过actionJson传递

        // 生成并保存actionJson字段
        if (content.actionType) {
          // 使用generate方法生成正确的actionJson格式
          const actionJson = ActionJsonGenerator.generate(
            content.actionType,
            content.actionUrl || '',
            content.actionPath || '',
            content
          );
          updatedContent.actionJson = actionJson;
        }
      }
      
      // 更新数组中的对象
      selectedTemplateContents.value[contentIndex] = updatedContent;
      
      console.log('已保存内容设置到索引', contentIndex, '类型:', updatedContent.type, 'actionType:', updatedContent.actionType);
    }
  } catch (error) {
    console.error('保存内容设置时出错:', error);
  }
};


// 初始化全局参数管理器
const initGlobalParamManager = () => {
  try {
    // console.log('初始化全局参数管理器...');
    
    // 强制清空全局变量，确保完全重置
    if (window.GLOBAL_PARAM_MANAGER) {
      delete window.GLOBAL_PARAM_MANAGER;
    }
    
    // 创建新的全局参数管理对象，统一管理参数ID
    window.GLOBAL_PARAM_MANAGER = createGlobalParamManager();
    
    // 使用延迟初始化，确保在打开模板编辑器后才扫描DOM
    if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.delayedInitialize === 'function') {
      nextTick(() => {
        window.GLOBAL_PARAM_MANAGER.delayedInitialize();
      });
    } else if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.initialize === 'function') {
      // 如果延迟初始化不可用，则使用普通初始化
      console.warn('延迟初始化方法不可用，使用普通初始化');
      window.GLOBAL_PARAM_MANAGER.initialize();
    } else {
      console.warn('参数管理器初始化方法不可用');
    }
    
    // 确保便捷函数可用
    window.getNextAvailableParamId = function() {
      if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.getNextId === 'function') {
        return window.GLOBAL_PARAM_MANAGER.getNextId();
      }
      console.error('全局参数管理器不可用，返回默认ID');
      return '1';
    };
    
    // console.log('全局参数管理器初始化完成');
  } catch (error) {
    console.error('初始化全局参数管理器时出错:', error);
    
    // 出错时创建最小化的参数管理器
    window.GLOBAL_PARAM_MANAGER = {
      usedParamIds: new Set(),
      getNextId: () => '1',
      recordParamUsage: () => {},
      markParamAsDeleted: () => {},
      resetState: () => {},
      initialize: () => {},
      getUsedIds: () => []
    };
    
    window.getNextAvailableParamId = function() { return '1'; };
  }
};

// 在方法部分添加handleEditTemplate方法
const handleEditTemplate = async (templateData) => {
  try {
    console.log('编辑模板数据:', templateData);
    
    // 构建提交数据
    const submitData = prepareTemplateData();
    
    // 确保包含templateId
    submitData.templateId = templateData.templateId;
    submitData.appKey = props.appKey;
    submitData.dirId = props.dirId;
    submitData.subType = props.subType;
    
    // 显示加载中提示
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '提交中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 调用API更新模板
    const res = await api.updateTemplate(submitData);
    console.log('更新模板响应:', res);
    
    // 关闭加载中提示
    loadingInstance.close();
    
    if (res.code === 0) {
      ElMessage.success('模板更新成功');
      
      // 关闭弹窗并重置数据
      resetEditState();
      
      // 关闭编辑器
      dialogVisible.value = false;
    } else {
      ElMessage.error(res.msg || '更新失败，请重试');
    }
  } catch (error) {
    console.error('更新模板错误:', error);
    ElMessage.error('更新模板出错，请重试');
  }
};

// 准备模板数据，用于提交到API
const prepareTemplateData = () => {
  // 检查是否为电商模板，如果是则使用专门的处理函数
  if (isEcommerceTemplate.value) {
    console.log('TemplateEditor - 检测到电商模板，使用专门的数据处理函数');
    const processedContents = processEcommerceTemplateData();
    
    const templateData = {
      templateName: templateName.value,
      cardId: selectedTemplate.value.cardId,
      scene: selectedTemplate.value.scene,
      factoryInfos: selectedTemplate.value.factoryInfos,
      // 添加用户上下文信息
      appKey: props.appKey,
      dirId: isEditMode.value ? (selectedTemplate.value.dirId || templateDirId.value) : props.dirId,
      pages: [{
        contents: processedContents
      }]
    };
    
    // 在编辑模式下添加templateId
    if (isEditMode.value && selectedTemplate.value && selectedTemplate.value.templateId) {
      templateData.templateId = selectedTemplate.value.templateId;
    }
    
    console.log('TemplateEditor - 电商模板提交数据准备完成:', templateData);
    return templateData;
  }

  // 检查是否为横滑模板，如果是则使用专门的处理函数
  if (isHorizontalSwipeTemplate.value) {
    console.log('TemplateEditor - 检测到横滑模板，使用专门的数据处理函数');
    const processedPages = processHorizontalSwipeTemplateData();
    
    const templateData = {
      templateName: templateName.value,
      cardId: selectedTemplate.value.cardId,
      scene: selectedTemplate.value.scene,
      factoryInfos: selectedTemplate.value.factoryInfos,
      // 添加用户上下文信息
      appKey: props.appKey,
      dirId: isEditMode.value ? (selectedTemplate.value.dirId || templateDirId.value) : props.dirId,
      pages: processedPages
    };
    
    // 在编辑模式下添加templateId
    if (isEditMode.value && selectedTemplate.value && selectedTemplate.value.templateId) {
      templateData.templateId = selectedTemplate.value.templateId;
    }
    
    console.log('TemplateEditor - 横滑模板提交数据准备完成:', templateData);
    return templateData;
  }
  
  // 检查是否为多商品模板，如果是则使用专门的处理函数
  if (isMultiProductTemplate.value) {
    console.log('TemplateEditor - 检测到多商品模板，使用专门的数据处理函数');
    
    // 使用 MultiProductTemplate 的新方法生成提交数据
    const processedContents = MultiProductTemplate.generateSubmitData(multiProductSettingsState.value);
    
    const templateData = {
      templateName: templateName.value,
      cardId: selectedTemplate.value.cardId,
      scene: selectedTemplate.value.scene,
      factoryInfos: selectedTemplate.value.factoryInfos,
      appKey: props.appKey,
      dirId: isEditMode.value ? (selectedTemplate.value.dirId || templateDirId.value) : props.dirId,
      pages: [{
        contents: processedContents
      }]
    };
    
    // 在编辑模式下添加templateId
    if (isEditMode.value && selectedTemplate.value && selectedTemplate.value.templateId) {
      templateData.templateId = selectedTemplate.value.templateId;
    }
    
    console.log('TemplateEditor - 多商品模板提交数据准备完成:', templateData);
    return templateData;
  }

  // 检查是否为券+商品模板，如果是则使用专门的处理函数
  if (isCouponProductTemplate.value) {
    console.log('TemplateEditor - 检测到券+商品模板，使用专门的数据处理函数');
    const processedContents = processCouponProductTemplateData();
    
    const templateData = {
      templateName: templateName.value,
      cardId: selectedTemplate.value.cardId,
      scene: selectedTemplate.value.scene,
      factoryInfos: selectedTemplate.value.factoryInfos,
      // 添加用户上下文信息
      appKey: props.appKey,
      dirId: isEditMode.value ? (selectedTemplate.value.dirId || templateDirId.value) : props.dirId,
      pages: [{
        contents: processedContents
      }]
    };
    
    // 在编辑模式下添加templateId
    if (isEditMode.value && selectedTemplate.value && selectedTemplate.value.templateId) {
      templateData.templateId = selectedTemplate.value.templateId;
    }
    
    console.log('TemplateEditor - 券+商品模板提交数据准备完成:', templateData);
    return templateData;
  }

  // 检查是否为通知类模板，如果是则使用专门的处理函数
  if (isNotificationTemplate.value) {
    console.log('TemplateEditor - 检测到通知类模板，使用专门的数据处理函数');
    
    // 使用 NotificationTemplate 的新方法生成提交数据
    const processedContents = NotificationTemplate.generateSubmitData(
      selectedTemplateContents.value,
      {
        maxVisibleParams: notificationSettings?.maxVisibleParams || 2,
        maxVisibleButtons: notificationSettings?.maxVisibleButtons || 2
      },
      {
        isEditMode: isEditMode.value,
        templateName: selectedTemplate.value?.templateName || ''
      }
    );
    
    const templateData = {
      templateName: templateName.value,
      cardId: selectedTemplate.value.cardId,
      scene: selectedTemplate.value.scene,
      factoryInfos: selectedTemplate.value.factoryInfos,
      appKey: props.appKey,
      dirId: isEditMode.value ? (selectedTemplate.value.dirId || templateDirId.value) : props.dirId,
      pages: [{
        contents: processedContents
      }]
    };
    
    // 在编辑模式下添加templateId
    if (isEditMode.value && selectedTemplate.value && selectedTemplate.value.templateId) {
      templateData.templateId = selectedTemplate.value.templateId;
    }
    
    console.log('TemplateEditor - 通知类模板提交数据准备完成:', templateData);
    return templateData;
  }

  // 检查是否为多图文模板，如果是则使用专门的处理函数
  if (isMultiTextTemplate.value) {
    console.log('TemplateEditor - 检测到多图文模板，使用专门的数据处理函数');
    // 获取当前多图文设置中的图文对数量
    const currentPairCount = multiTextSettings?.pairCount || 1;
    console.log('TemplateEditor - 提交时使用的图文对数量:', currentPairCount);

    const processedContents = MultiTextTemplate.generateSubmitData(
      selectedTemplateContents.value,
      { pairCount: currentPairCount },
      { isEditMode: isEditMode.value }
    );
    const templateData = {
      templateName: templateName.value,
      cardId: selectedTemplate.value.cardId,
      scene: selectedTemplate.value.scene,
      factoryInfos: selectedTemplate.value.factoryInfos,
      appKey: props.appKey,
      dirId: isEditMode.value ? (selectedTemplate.value.dirId || templateDirId.value) : props.dirId,
      pages: [{ contents: processedContents }]
    };
    if (isEditMode.value && selectedTemplate.value && selectedTemplate.value.templateId) {
      templateData.templateId = selectedTemplate.value.templateId;
    }
    console.log('TemplateEditor - 多图文模板提交数据准备完成:', templateData);
    return templateData;
  }

  // 非电商和非横滑模板的原有处理逻辑
  // 深度复制内容对象，防止修改原对象
  let processedContents = [];
  
  // 对于通知类模板，需要根据用户设置过滤按钮数量
  let contentsToProcess = selectedTemplateContents.value;
  
  // 遍历需要处理的内容
  contentsToProcess.forEach(content => {
    // 深度复制当前内容
    const processedContent = JSON.parse(JSON.stringify(content));
    
    // 处理内容中的特殊字段
    // 在编辑模式下保留contentId，新建模式下删除
    if (!isEditMode.value) {
      delete processedContent.contentId;
    } else if (!processedContent.contentId) {
      processedContent.contentId = generateUniqueId();
    }
    
    // 处理可能有HTML标签的文本内容
    if (processedContent.type === 'text' && processedContent.editContent) {
      processedContent.content = cleanTextContent(processedContent.editContent);
    }
    
    // 如果是图片类型，可以不传content或传空
    if (processedContent.type === 'image') {
      processedContent.content = '';
      
      // 处理轮播图数据
      if (processedContent.isCarousel && processedContent.carouselImages && Array.isArray(processedContent.carouselImages)) {
        console.log('处理轮播图数据，图片数量:', processedContent.carouselImages.length);
        
        // 处理轮播图中每张图片的数据
        processedContent.carouselImages.forEach((image, index) => {
          // 确保positionNumber正确分配：第一张图片为1，后续新增的图片从5开始
          let correctPositionNumber;
          if (index === 0) {
            // 第一张图片的positionNumber始终为1
            correctPositionNumber = 1;
          } else {
            // 新增的图片从5开始，依次递增
            correctPositionNumber = 4 + index;
          }
          
          const processedImage = {
            src: image.src,
            // 强制使用正确的positionNumber，不保留原有的错误值
            positionNumber: correctPositionNumber,
          };
          
          // 处理每张图片的点击事件
          if (image.clickEvent && image.clickEvent.type !== 'none') {
            const clickEvent = image.clickEvent;
            
            // 使用统一的方法处理actionType
            processedImage.actionType = ClickEventTypeConverter.toActionType(clickEvent.type);
            
            // 使用统一的方法生成actionJson
            processedImage.actionJson = ActionJsonGenerator.fromClickEvent(clickEvent);
            
            console.log(`轮播图第${index + 1}张图片点击事件处理完成:`, {
              原始事件: clickEvent,
              处理后actionType: processedImage.actionType,
              处理后actionJson: processedImage.actionJson
            });
          } else {
            // 没有点击事件或事件类型为none
            processedImage.actionType = 'NONE';
            processedImage.actionJson = { target: '' };
          }
          
          // 将轮播图片作为独立的content项添加到结果数组中
          const carouselContentItem = {
            type: 'image',
            src: processedImage.src,
            // 移除alt字段
            positionNumber: processedImage.positionNumber,
            isTextTitle: 0,
            actionType: processedImage.actionType,
            actionJson: processedImage.actionJson
          };
          
          // 将轮播图片添加到处理后的内容数组中
          processedContents.push(carouselContentItem);
          
          console.log(`轮播图第${index + 1}张图片已添加到提交数据:`, carouselContentItem);
        });
        
        return;
      }
    }
    
    // 如果是视频类型，不传content字段
    if (processedContent.type === 'video') {
      delete processedContent.content;
    }
    
    // 保留必要的字段，只保留actionJson，不传递重复的外层字段
    const necessaryFields = [
      'type', 'contentId', 'src', 'positionNumber', 'isTextTitle'
    ];
    
    // 视频类型需要aimCover字段，但不需要content字段
    if (processedContent.type === 'video') {
      necessaryFields.push('aimCover');
    } else {
      // 其他类型需要content字段
      necessaryFields.push('content');
    }
    
    // 只有可点击的内容类型才添加点击事件相关字段（移除video类型）
    if (processedContent.type === 'button' || processedContent.type === 'image') {
      necessaryFields.push('actionType', 'actionJson');

      // 如果是复制内容类型，重新生成正确的 actionJson
      if (processedContent.actionType === 'COPY_PARAMETER') {
        processedContent.actionJson = ActionJsonGenerator.generate(
          processedContent.actionType,
          '',
          '',
          {
            copyType: processedContent.copyType || '2',
            selectedParamId: processedContent.selectedParamId || '',
            fixedContent: processedContent.fixedContent || ''
          }
        );
      }
    }
    // 为价格类型的文本保留aimCurrencyDisplay字段
    // 2. 单卡券模板专用条件：positionNumber === 1 的文本（仅限单卡券模板）
    if (processedContent.type === 'text' && (isCardVoucherTemplate.value && processedContent.positionNumber === 1)) {
      necessaryFields.push('aimCurrencyDisplay');
    }

    const cleanContent = {};
    necessaryFields.forEach(field => {
      // 修复positionNumber为0时被过滤的问题
      if (field === 'positionNumber') {
        // positionNumber允许为0，只要不是undefined或null
        if (processedContent[field] !== undefined && processedContent[field] !== null) {
          cleanContent[field] = processedContent[field];
        }
      } else if (field === 'aimCover') {
        // aimCover字段即使为空字符串也保留，只要不是undefined或null
        if (processedContent[field] !== undefined && processedContent[field] !== null) {
          cleanContent[field] = processedContent[field];
        }
      } else if (field === 'aimCurrencyDisplay') {
        // 确保aimCurrencyDisplay字段被正确传递
        cleanContent[field] = processedContent[field] !== undefined ? processedContent[field] : 1;
      } else {
        // 其他字段保持原有逻辑
        if (processedContent[field] !== undefined && processedContent[field] !== null && processedContent[field] !== '') {
          cleanContent[field] = processedContent[field];
        }
      }
    });
    
    // 将处理后的内容添加到结果数组中
    processedContents.push(cleanContent);
  });
  
  // 过滤不需要的内容
  processedContents = processedContents.filter(content => {
    // 判断是否是红包模板
    const isRedPacket = isRedPacketTemplate(selectedTemplate.value);
    // 过滤条件：红包模板中，类型为background且src为指定默认图的内容项
    if (isRedPacket && content.type === 'background' && content.src === '/aim_files/aim_defult/redpacket_defaultimg.png') {
      return false;
    }
    
    // 判断是否是多图文模板
    const isMultiText = isMultiTextTemplate.value;
    if (isMultiText) {
      // 过滤默认图片：src包含defaultImg.jpg的图片内容
      if (content.type === 'image' && content.src && content.src.includes('defaultImg.jpg')) {
        console.log('多图文模板过滤默认图片:', content);
        return false;
      }
      
      // 过滤默认标题文本：包含默认文本的内容
      if (content.type === 'text') {
        const defaultTexts = [
          '编辑文本，最多显示30个字。编辑文本，最多显示30个字。',
        ];
        
        // 多图文模板的头部文本（positionNumber: 2）即使包含默认文本也要保留
        if (content.positionNumber === 2 && content.isTextTitle === 1) {
          console.log('多图文模板保留头部文本（positionNumber: 2）:', content);
          // 不过滤头部文本，继续处理
        } else if (content.content && defaultTexts.some(defaultText => content.content.includes(defaultText))) {
          console.log('多图文模板过滤默认文本:', content);
          return false;
        }
      }
    }
     // 过滤掉positionNumber为0的背景元素（单卡券模板不需要传递背景）
    if (isCardVoucherTemplate.value && content.positionNumber === 0) {
      return false;
    }
    
    return true;
  }).filter(content => content !== null);

  const templateData = {
    templateName: templateName.value,
    cardId: selectedTemplate.value.cardId,
    scene: selectedTemplate.value.scene,
    factoryInfos: selectedTemplate.value.factoryInfos,
    // 添加用户上下文信息
    appKey: props.appKey,
    dirId: isEditMode.value ? (selectedTemplate.value.dirId || templateDirId.value) : props.dirId,
    // 如果是编辑模式且原模板有userId，保留原userId；否则使用当前用户信息
    // userId: isEditMode.value ? selectedTemplate.value.userId : null,
    pages: [{
      contents: processedContents
    }]
  };
  
  // 在编辑模式下添加templateId
  if (isEditMode.value && selectedTemplate.value && selectedTemplate.value.templateId) {
    templateData.templateId = selectedTemplate.value.templateId;
  }
  return templateData;
};

// 清理文本内容，去除HTML标签，保留纯文本和参数
const cleanTextContent = (htmlContent) => {
  if (!htmlContent) return '';
  
  // 创建临时容器解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;
  
  // 提取文本和参数
  let cleanedContent = '';
  
  // 处理所有子节点
  for (const node of tempDiv.childNodes) {
    if (node.nodeType === Node.TEXT_NODE) {
      // 文本节点直接添加
      cleanedContent += node.textContent;
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      // 如果是参数按钮元素
      if ((node.tagName === 'INPUT' || node.tagName === 'SPAN') && 
          (node.classList.contains('j-btn') || node.classList.contains('param-input'))) {
        // 添加参数值
        cleanedContent += node.value || node.textContent;
      } else {
        // 其他元素递归提取文本
        cleanedContent += node.textContent;
      }
    }
  }
  
  return cleanedContent;
};

// 生成唯一ID
const generateUniqueId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
};

// 关闭模板编辑器
const close = () => {
  // 清除复制模式标识
  window._isCopyMode = false;

  // 清除复制模板的原始数据
  copyTemplateOriginalData.value = null;

  dialogVisible.value = false;
  resetEditState();
};

// 打开编辑模式
const openEdit = async (templateData) => {
  console.log('进入编辑模式，编辑模板:', templateData);
  dialogVisible.value = true;
  isEditMode.value = true; // 明确设置为编辑模式
  
  // 重置所有模板设置状态（确保打开编辑模式时状态被重置，避免数据污染）
  ecommerceSettingsState.value = null;
  multiProductSettingsState.value = null;
  couponProductSettingsState.value = null;

  // 重置全局校验失败标志，避免影响其他模板
  window._isEcommerceValidationFailed = false;
  console.log('TemplateEditor - 打开编辑模式，重置所有模板设置状态和全局标志');
  
  selectedTemplate.value = templateData;
  templateName.value = templateData.templateName || '';
  templateDirId.value = templateData.dirId || '';
  
  try {
    // 重新获取模板数据
    await fetchTemplateData();
    // 等待模板数据加载完成后再设置选中模板
    selectedTemplate.value = templateData;

    // 初始化中间预览区域的数据
    const templateType = templateFactory.detectTemplateType(templateData);
    const templateHandler = templateFactory.getTemplateHandler(templateType);
    if (templateHandler && templateHandler.initializeContents) {
      if (templateType === 'longtext') {
        // 长文本模板需要传入settings参数
        selectedTemplateContents.value = templateHandler.initializeContents(templateData, longTextSettings);
      } else {
        selectedTemplateContents.value = templateHandler.initializeContents(templateData);
      }
    } else {
      // 深拷贝模板数据
      const contentsCopy = JSON.parse(JSON.stringify(templateData.pages?.[0]?.contents || []));
      
      // 处理每个内容项
      selectedTemplateContents.value = contentsCopy.map(content => {
        // 每个内容创建新的contentId，确保唯一性
        content.contentId = generateUniqueId();
        
        // 文本内容特殊处理
        if (content.type === 'text') {
          // 编辑模式下保留原始内容
          content.originalContent = content.content;
          content.editContent = content.content; // 设置编辑内容为当前内容
          content.isEditing = false;
        }
        
        // 图片内容特殊处理
        if (content.type === 'image' || content.type === 'background' || content.type === 'video') {
          content.defaultSrc = content.src; // 保存原始图片路径
          content.userEdited = false; // 初始化为未修改状态
          
          // 检查是否从视频模板切换到其他模板，需要将视频内容恢复为图片
          if (content.type === 'video') {
            // 如果当前选择的不是视频模板，但内容类型是video，将其转换为image
            const isCurrentTemplateVideo = templateData.cardId === 'com.hbm.video';
            if (!isCurrentTemplateVideo) {
              content.type = 'image';
              // 恢复为默认图片
              content.src = '/aim_files/aim_defult/defaultImg.jpg';
              content.defaultSrc = '/aim_files/aim_defult/defaultImg.jpg';
            }
          }
          
          // 在新建模式下，如果使用了默认图片路径，标记为需要修改
          if (!isEditMode.value && content.src && (
              content.src.includes('/aim_files/aim_defult/') || 
              content.src.includes('defaultImg.jpg') ||
              content.src.includes('defaultVideo.jpg')
          )) {
            content.userEdited = true;
            // console.log('新建模式：检测到默认图片，标记为需要修改:', content.src);
          }
        }

        // 判断是否是红包模板
        const isRedPacket = templateData.templateName?.includes('红包') || templateData.scene?.includes('红包');
        window.TEMPLATE_IS_REDPACKET = isRedPacket; // 设置全局变量
        
        // 背景类型初始化 aimOppoBackground
        if (content.type === 'background') {
          content.aimOppoBackground = content.aimOppoBackground || ''; // 若已有值则保留，否则初始化为空
        }
        
        // 确保有默认的actionType
        if (content.type === 'image' || content.type === 'button') {
          content.actionType = content.actionType || 'OPEN_BROWSER';
        }
        
        return content;
      });
    }

    // 确保选中第一个内容项
    if (selectedTemplateContents.value.length > 0) {
      selectedContent.value = selectedTemplateContents.value[0];
    }

    console.log('编辑模式初始化完成，isEditMode:', isEditMode.value);
  } catch (error) {
    console.error('打开编辑模式时获取模板数据失败:', error);
  }
};

// 处理横滑模板数据，按照正确的格式组织数据
const processHorizontalSwipeTemplateData = () => {
  console.log('=== processHorizontalSwipeTemplateData 开始 ===');
  console.log('selectedTemplateContents.value:', selectedTemplateContents.value);
  // 获取页面级别的点击事件数据
  let pagesData = [];
  if (selectedTemplate.value && selectedTemplate.value.pages) {
    pagesData = parsePages(selectedTemplate.value.pages);
    console.log('获取到的页面数据:', pagesData);
  }
  
  // 按pageIndex分组
  const pageGroups = {};
  
  selectedTemplateContents.value.forEach(content => {
    const pageIndex = content.pageIndex || 0;
    if (!pageGroups[pageIndex]) {
      pageGroups[pageIndex] = [];
    }
    
    // 深度复制内容，避免修改原对象
    const processedContent = JSON.parse(JSON.stringify(content));
    
    // 获取当前页面的点击事件数据
    const currentPageData = pagesData[pageIndex];
    const pageClickEvent = currentPageData?.clickEvent;
    
    console.log(`处理第${pageIndex}页内容:`, {
      内容类型: processedContent.type,
      页面点击事件: pageClickEvent,
      内容点击事件: processedContent.clickEvent
    });
    
    // 处理内容中的特殊字段
    if (!isEditMode.value) {
      delete processedContent.contentId;
    } else if (!processedContent.contentId) {
      processedContent.contentId = generateUniqueId();
    }

    // 处理可能有HTML标签的文本内容
    if (processedContent.type === 'text' && processedContent.editContent) {
      processedContent.content = cleanTextContent(processedContent.editContent);
    }

    // 如果是图片类型，不传content字段，并处理点击事件
    if (processedContent.type === 'image') {
      // 删除content字段，图片类型不需要content
      delete processedContent.content;
      
      // 横滑模板中的图片统一设置为空值，因为图片不需要单独的点击事件
      processedContent.actionType = '';
      processedContent.actionJson = '';
      
      console.log('横滑模板图片点击事件已设置为空值:', {
        类型: processedContent.type,
        actionType: processedContent.actionType,
        actionJson: processedContent.actionJson
      });
    }

    // 如果是按钮类型，处理点击事件 - 使用 clickEventManager.js 的统一方法
    if (processedContent.type === 'button') {
      // 优先使用页面级别的点击事件，如果没有则使用按钮自身的点击事件
      const buttonClickEvent = pageClickEvent || processedContent.clickEvent;
      
      if (buttonClickEvent && buttonClickEvent.type && buttonClickEvent.type !== 'none') {
        console.log('处理按钮点击事件:', buttonClickEvent);
        
        // 使用 clickEventManager.js 的统一方法处理 actionType
        processedContent.actionType = ClickEventTypeConverter.toActionType(buttonClickEvent.type);
        
        // 使用 clickEventManager.js 的统一方法生成 actionJson
        processedContent.actionJson = ActionJsonGenerator.fromClickEvent(buttonClickEvent);
        
        console.log('按钮点击事件处理完成:', {
          页面索引: pageIndex,
          原始事件: buttonClickEvent,
          actionType: processedContent.actionType,
          actionJson: processedContent.actionJson
        });
      } else {
        // 按钮必须有点击事件，设置默认值
        processedContent.actionType = 'OPEN_BROWSER';
        processedContent.actionJson = ActionJsonGenerator.generate('OPEN_BROWSER', '', '', {});
        console.log('按钮没有点击事件，使用默认值:', {
          页面索引: pageIndex,
          actionType: processedContent.actionType,
          actionJson: processedContent.actionJson
        });
      }
    }

    // 清理不需要的字段
    delete processedContent.pageIndex;
    delete processedContent.clickEvent;
    delete processedContent.userId;
    delete processedContent.editContent;
    delete processedContent.originalContent;
    delete processedContent.isEditing;
    delete processedContent.defaultSrc;
    delete processedContent.userEdited;

    // 只保留必要的字段
    const necessaryFields = ['type', 'src', 'content', 'positionNumber', 'isTextTitle', 'actionType', 'actionJson'];
    const cleanContent = {};
    
    necessaryFields.forEach(field => {
      if (processedContent[field] !== undefined && processedContent[field] !== null) {
        // 对于图片类型，不包含content字段
        if (field === 'content' && processedContent.type === 'image') {
          return;
        }
        // 对于actionType和actionJson，图片和按钮类型都保留（包括空字符串）
        if ((field === 'actionType' || field === 'actionJson')) {
          if (processedContent.type === 'button' || processedContent.type === 'image') {
            // 按钮和图片类型的actionType和actionJson都保留，包括空字符串
            cleanContent[field] = processedContent[field];
          }
          return;
        }
        cleanContent[field] = processedContent[field];
      }
    });

    // 在编辑模式下保留contentId
    if (isEditMode.value && processedContent.contentId) {
      cleanContent.contentId = processedContent.contentId;
    }

    pageGroups[pageIndex].push(cleanContent);
  });

  // 将分组的内容转换为pages数组
  const pages = [];
  const sortedPageIndexes = Object.keys(pageGroups).sort((a, b) => parseInt(a) - parseInt(b));
  
  sortedPageIndexes.forEach(pageIndex => {
    const contents = pageGroups[pageIndex];
    // 按positionNumber排序
    contents.sort((a, b) => (a.positionNumber || 0) - (b.positionNumber || 0));
    
    pages.push({
      contents: contents
    });
  });

  console.log('=== processHorizontalSwipeTemplateData 完成 ===');
  console.log('生成的pages数据:', pages);
  
  return pages;
};

// 处理模板提交
const onConfirmSubmit = async (formData) => {
  try {
    console.log('确认提交模板', formData);
    
    // 在准备数据前，强制同步最新的单卡券设置数据
    if (isCardVoucherTemplate.value) {
      console.log('TemplateEditor - 单卡券模板提交前，强制同步最新数据');
      console.log('TemplateEditor - 当前selectedTemplateContents:', JSON.parse(JSON.stringify(selectedTemplateContents.value)));
      
      // 检查全局是否有最新的单卡券数据
      if (window.CARDVOUCHER_LATEST_DATA) {
        console.log('TemplateEditor - 发现全局最新数据，使用最新数据:', window.CARDVOUCHER_LATEST_DATA);
        selectedTemplateContents.value = [...window.CARDVOUCHER_LATEST_DATA];
      }
      
      // 发出全局事件，强制CardVoucherSettings同步最新数据
      const syncEvent = new CustomEvent('force-cardvoucher-sync');
      document.dispatchEvent(syncEvent);
      
      // 等待一下确保数据同步完成
      await new Promise(resolve => setTimeout(resolve, 200));
      
      console.log('TemplateEditor - 强制同步后的内容数据:', JSON.parse(JSON.stringify(selectedTemplateContents.value)));
      
      // 验证数据是否包含用户修改的内容
      const textContents = selectedTemplateContents.value.filter(content => content.type === 'text');
      console.log('TemplateEditor - 提交前文本内容检查:', textContents.map(content => ({
        positionNumber: content.positionNumber,
        content: content.content,
        editContent: content.editContent
      })));
      
      // 如果数据还是默认数据，再次尝试强制获取最新数据
      const hasDefaultContent = textContents.some(content => 
        content.content && content.content.includes('编辑文本，最')
      );
      
      if (hasDefaultContent) {
        console.log('TemplateEditor - 检测到默认数据，再次尝试强制同步');
        // 再次尝试从全局状态获取数据
        if (window.CARDVOUCHER_LATEST_DATA) {
          selectedTemplateContents.value = JSON.parse(JSON.stringify(window.CARDVOUCHER_LATEST_DATA));
          console.log('TemplateEditor - 使用备用全局数据:', selectedTemplateContents.value);
        }
      }
    }
    
    // 准备提交数据
    const submitData = prepareTemplateData();
    
    console.log('TemplateEditor - 准备提交的数据:', JSON.parse(JSON.stringify(submitData)));

    // 合并预览表单数据
    if (formData) {
      submitData.useId = formData.useId;
      // 处理 aimSmsSigns
      if (Array.isArray(formData.aimSmsSigns)) {
        submitData.aimSmsSigns = formData.aimSmsSigns.map(sign => `"${sign}"`).join(',');
      } else {
        submitData.aimSmsSigns = formData.aimSmsSigns;
      }
      submitData.smsExample = formData.smsExample;
      submitData.scene = formData.scene;
      
      // 添加state参数,从原模板中获取
      if (selectedTemplate.value && selectedTemplate.value.state) {
        submitData.state = selectedTemplate.value.state;
      }
      
      // 添加subType参数
      console.log('TemplateEditor onConfirmSubmit: 设置 subType，props.subType 值为:', props.subType);
      submitData.subType = props.subType;
      console.log('TemplateEditor onConfirmSubmit: 设置后 submitData.subType 值为:', submitData.subType);
      
      if (formData.factoryInfos) {
        const selectedfactoryInfos = [];
        Object.entries(formData.factoryInfos).forEach(([key, value]) => {
          if (value) {
            selectedfactoryInfos.push(key);
          }
        });
        submitData.factoryInfos = selectedfactoryInfos;
      }
    }

    // 处理 pages 数组中的每个页面
    if (submitData.pages && Array.isArray(submitData.pages)) {
      submitData.pages = JSON.stringify(submitData.pages);
    }
    
    // 所需的其他字段
    submitData.appKey = props.appKey;
    
    // 判断是编辑模式还是新建模式来决定使用哪个dirId
    if (isEditMode.value && selectedTemplate.value && selectedTemplate.value.templateId) {
      // 编辑模式：使用模板自身的dirId
      submitData.dirId = selectedTemplate.value.dirId || templateDirId.value;
    } else {
      // 新建模式：使用props中传入的dirId
      submitData.dirId = props.dirId;
    }
    
    // 显示加载中提示
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '提交中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    console.log('提交模板数据:', submitData);
    
    try {
      let res;
      // 判断是新增还是更新
      if (isEditMode.value && selectedTemplate.value && selectedTemplate.value.templateId) {
        // 编辑模式
        submitData.templateId = selectedTemplate.value.templateId;
        res = await api.updateTemplate(submitData);
      } else {
        // 新增模式
        res = await api.addTemplate(submitData);
      }
      
      console.log('提交模板响应:', res);
      
      // 关闭加载中提示
      loadingInstance.close();
      
      if (res.code === 0) {
        ElMessage.success(selectedTemplate.value.templateId ? '更新成功' : '提交成功');
        
        // 关闭弹窗并重置数据
        resetEditState();
        
        // 关闭预览提交弹窗
        previewSubmitDialogVisible.value = false;
        
        // 关闭编辑器
        dialogVisible.value = false;
        
        // 获取完整的模板数据
        const templateData = {
          templateId: isEditMode.value ? selectedTemplate.value.templateId : res.data?.templateId,
          isEdit: isEditMode.value,
          state: submitData.state,
          templateName: submitData.templateName, // 确保包含正确的模板名称
          pages: typeof submitData.pages === 'string' ? JSON.parse(submitData.pages) : submitData.pages, // 确保pages是对象格式
          actionJson: submitData.actionJson, // 确保包含actionJson
          actionType: submitData.actionType,
          actionUrl: submitData.actionUrl,
          actionPath: submitData.actionPath,
          // 添加其他点击事件相关字段
          packageName: submitData.packageName,
          floorType: submitData.floorType,
          // 暂时注释掉hwAppId和ryAppId字段
          // hwAppId: submitData.hwAppId,
          // ryAppId: submitData.ryAppId
        };
        
        // 提交成功后触发更新事件
        const updateEvent = new CustomEvent('template-updated', {
          detail: templateData
        });
        window.dispatchEvent(updateEvent);
        
        // 如果有全局事件总线，也通过事件总线发送
        if (window.TEMPLATE_EVENT_BUS) {
          window.TEMPLATE_EVENT_BUS.emit('template-updated', templateData);
        }
        
        // 通知父组件提交成功
        emit('submit', {
          success: true,
          isEdit: isEditMode.value,
          data: templateData
        });
        
        // 强制刷新列表
        emit('refresh-list');
      } else {
        ElMessage.error(res.msg || (selectedTemplate.value.templateId ? '更新失败' : '提交失败') + '，请重试');
      }
    } catch (error) {
      console.error('提交模板错误:', error);
      ElMessage.error((selectedTemplate.value.templateId ? '更新' : '提交') + '模板出错，请重试');
      
      // 关闭加载中提示
      loadingInstance.close();
    }
  } catch (error) {
    console.error('提交模板错误:', error);
    ElMessage.error('提交失败，请重试');
  }
};

// 应用模板变更
const applyTemplateChange = (template) => {
  try {
    console.log('应用模板变更:', template);
    
    // 重置全局状态
    window.TEMPLATE_IS_REDPACKET = false;
    window.TEMPLATE_DIALOG_DATA = {};

    // 清除复制模式标识，应用模板变更后使用新建模板的逻辑
    window._isCopyMode = false;
    
    // 在更新selectedTemplate之前，先保存旧模板信息用于比较
    const oldTemplate = selectedTemplate.value;
    const currentTemplateType = oldTemplate ? templateFactory.detectTemplateType(oldTemplate) : null;

    // 清空当前内容并设置新模板
    selectedTemplate.value = cloneDeep(template);
    selectedContent.value = null;

    // 重置多图文设置为默认值
    multiTextSettings.pairCount = 1;
    console.log('重置多图文设置为默认值: 1组图文对');

    // 重置所有模板类型的设置状态，避免数据污染
    ecommerceSettingsState.value = null;
    multiProductSettingsState.value = null;
    couponProductSettingsState.value = null;
    console.log('TemplateEditor - 重置所有模板设置状态，避免数据污染');

    // 重置所有设置面板的显示状态
    showEcommerceSettings.value = false;
    showMultiProductSettings.value = false;
    showCouponProductSettings.value = false;
    showCardVoucherSettings.value = false;

    // 重置全局校验失败标志，避免影响其他模板
    window._isEcommerceValidationFailed = false;
    console.log('TemplateEditor - 重置全局校验失败标志');

    // 检查是否是真正的模板切换（而不是横滑模板内部的卡片切换）
    const isRealTemplateSwitch = !oldTemplate ||
                                oldTemplate.cardId !== template.cardId ||
                                oldTemplate.templateName !== template.templateName;

    console.log('是否为真正的模板切换:', isRealTemplateSwitch);

    // 检查是否是不同模板类型之间的切换
    const newTemplateType = templateFactory.detectTemplateType(template);
    const isTemplateTypeSwitch = currentTemplateType !== newTemplateType;

    console.log('模板类型切换检查:', {
      当前模板类型: currentTemplateType,
      新模板类型: newTemplateType,
      是否为模板类型切换: isTemplateTypeSwitch
    });

    // 在真正的模板切换或模板类型切换时清空点击事件缓存
    if (isRealTemplateSwitch || isTemplateTypeSwitch) {
      console.log('检测到模板切换，清空点击事件缓存');
      window.CLICK_EVENT_CACHE = {};

      // 重置全局参数记录
      if (window.GLOBAL_PARAM_NUMBERS) {
        console.log('重置全局参数记录');
        window.GLOBAL_PARAM_NUMBERS.clear();
      }

      // 触发模板切换和重置事件
      dispatchTemplateSwitchEvent();
      dispatchTemplateReset();
    } else {
      console.log('横滑模板内部操作，不清空点击事件缓存');
    }
    
    // 判断是否为红包模板，并设置全局标记
    if (isRedPacketTemplate(template)) {
      console.log('选择了红包模板:', template.templateName || template.scene);
      window.TEMPLATE_IS_REDPACKET = true;
      
      // 保存模板信息到全局变量
      window.TEMPLATE_DIALOG_DATA = {
        selectedTemplate: template,
        isRedPacket: true
      };
    }
    
    // 检测模板类型
    const templateType = templateFactory.detectTemplateType(template);
    console.log('检测到的模板类型2:', templateType);
    console.log('模板cardId:', template?.cardId);
    
    // 初始化模板内容
    if (templateType === 'horizontalswipe') {
      // 横滑模板特殊处理：使用模板处理器初始化内容
      const templateHandler = templateFactory.getTemplateHandler(templateType);
      if (templateHandler && templateHandler.initializeContents) {
        const initialContents = templateHandler.initializeContents(template);
        selectedTemplateContents.value = cloneDeep(initialContents).map(content => ({
          ...content,
          contentId: content.contentId || generateUniqueId(),
          userId: template.userId // 从顶层模板数据注入userId
        }));
      } else {
        console.warn('横滑模板处理器未找到或没有initializeContents方法');
        selectedTemplateContents.value = [];
      }
    } else if (templateType === 'multiproduct') {
      // 多商品模板特殊处理：使用模板处理器初始化内容
      const templateHandler = templateFactory.getTemplateHandler(templateType);
      if (templateHandler && templateHandler.initializeContents) {
        const initialContents = templateHandler.initializeContents(template);
        selectedTemplateContents.value = cloneDeep(initialContents).map(content => ({
          ...content,
          contentId: content.contentId || generateUniqueId(),
          userId: template.userId // 从顶层模板数据注入userId
        }));
        console.log('多商品模板 - selectedTemplateContents 已初始化:', selectedTemplateContents.value);
      } else {
        console.warn('多商品模板处理器未找到或没有initializeContents方法');
        selectedTemplateContents.value = [];
      }
    } else if (templateType === 'couponproduct') {
      // 券+商品模板特殊处理：使用模板处理器初始化内容
      const templateHandler = templateFactory.getTemplateHandler(templateType);
      if (templateHandler && templateHandler.initializeContents) {
        const initialContents = templateHandler.initializeContents(template);
        selectedTemplateContents.value = cloneDeep(initialContents).map(content => ({
          ...content,
          contentId: content.contentId || generateUniqueId(),
          userId: template.userId // 从顶层模板数据注入userId
        }));
        console.log('券+商品模板 - selectedTemplateContents 已初始化:', selectedTemplateContents.value);
      } else {
        console.warn('券+商品模板处理器未找到或没有initializeContents方法');
        selectedTemplateContents.value = [];
      }
    } else if (templateType === 'cardvoucher') {
      // 单卡券模板特殊处理：使用模板处理器初始化内容
      const templateHandler = templateFactory.getTemplateHandler(templateType);
      if (templateHandler && templateHandler.initializeContents) {
        const initialContents = templateHandler.initializeContents(template);
        selectedTemplateContents.value = cloneDeep(initialContents).map(content => ({
          ...content,
          contentId: content.contentId || generateUniqueId(),
          userId: template.userId // 从顶层模板数据注入userId
        }));
        console.log('单卡券模板 - selectedTemplateContents 已初始化:', selectedTemplateContents.value);
      } else {
        console.warn('单卡券模板处理器未找到或没有initializeContents方法');
        selectedTemplateContents.value = [];
      }
    } else if (Array.isArray(template.pages) && template.pages.length > 0 && template.pages[0].contents) {
      // 其他模板的常规处理
      // 深拷贝内容，防止修改原始对象
      const contentsCopy = JSON.parse(JSON.stringify(template.pages[0].contents));
      
      // 设置新内容
      selectedTemplateContents.value = cloneDeep(contentsCopy).map(content => {
        // 每个内容创建新的contentId，确保唯一性
        content.contentId = generateUniqueId();
        
        // 初始化内容项
        if (content.type === 'text') {
          content.originalContent = content.content; // 保存原始内容
          content.isEditing = false; // 添加编辑状态标记
          content.editContent = undefined; // 初始状态为undefined
        }
        
        // 初始化图片类型内容
        if (content.type === 'image' || content.type === 'background' || content.type === 'video') {
          content.defaultSrc = content.src; // 保存原始图片路径
          content.userEdited = false; // 明确初始化编辑状态
          
          // 检查是否从视频模板切换到其他模板，需要将视频内容恢复为图片
          if (content.type === 'video') {
            // 如果当前选择的不是视频模板，但内容类型是video，将其转换为image
            const isCurrentTemplateVideo = template.cardId === 'com.hbm.video';
            if (!isCurrentTemplateVideo) {
              content.type = 'image';
              // 恢复为默认图片
              content.src = '/aim_files/aim_defult/defaultImg.jpg';
              content.defaultSrc = '/aim_files/aim_defult/defaultImg.jpg';
            }
          }
          
          // 在新建模式下，如果使用了默认图片路径，标记为需要修改
          if (!isEditMode.value && content.src && (
              content.src.includes('/aim_files/aim_defult/') || 
              content.src.includes('defaultImg.jpg') ||
              content.src.includes('defaultVideo.jpg')
          )) {
            content.userEdited = true;
            // console.log('新建模式：检测到默认图片，标记为需要修改:', content.src);
          }
        }
        
        // 确保有默认的actionType
        if (content.type === 'image' || content.type === 'button') {
          content.actionType = content.actionType || 'OPEN_BROWSER';
        }
        
        // 清除可能的点击事件缓存残留
        if (content.type === 'image' || content.type === 'button') {
          // 初始化点击事件缓存
          const cacheKey = getCacheKey(content);
          if (!window.CLICK_EVENT_CACHE[cacheKey]) {
            window.CLICK_EVENT_CACHE[cacheKey] = {
              actionType: content.actionType || 'OPEN_BROWSER',
              actionUrl: content.actionUrl || '',
              actionPath: content.actionPath || ''
            };
          }
        }
        
        return content;
      });
      
      console.log('模板内容设置完成，编辑模式:', isEditMode.value);
      
      // 初始化参数显示
      nextTick(() => {
        initializeContentParams();
      });
    } else {
      selectedTemplateContents.value = [];
    }
    
    // 重置编辑状态
    hasChanges.value = false;
    hasEditedContent.value = false;
    
    return true;
  } catch (error) {
    console.error('应用模板变更失败:', error);
    ElMessage.error('切换模板失败，请重试');
    return false;
  }
};

// 初始化内容参数
const initializeContentParams = () => {
  try {
    console.log('正在初始化内容参数显示...');
    
    // 处理所有文本和描述内容
    if (selectedTemplateContents.value && selectedTemplateContents.value.length > 0) {
      selectedTemplateContents.value.forEach((content, index) => {
        if (content.type === 'text') {
          // 查找文本内容元素
          let selector;
          if (content.isTextTitle === 1) {
            selector = '.preview-title';
          } else if (isRedPacketTemplate(selectedTemplate.value) && content.positionNumber === 2) {
            selector = '.preview-name.red-packet-name';
          } else {
            selector = '.preview-desc';
          }
          
          const contentElements = document.querySelectorAll(selector);
          
          // 如果有多个元素，选择正确的那个
          let contentElement = null;
          if (contentElements.length === 1) {
            contentElement = contentElements[0];
          } else if (contentElements.length > 1) {
            // 尝试查找对应索引的元素
            contentElement = contentElements[index] || contentElements[0];
          }
          
          if (contentElement && content.content) {
            // 设置原始内容，确保不会丢失默认状态
            if (!content.originalContent) {
              content.originalContent = content.content;
            }
            
            console.log(`正在处理${content.isTextTitle === 1 ? '标题' : isRedPacketTemplate(selectedTemplate.value) && content.positionNumber === 2 ? '红包名称' : '描述'}内容:`, content.content);
            
            // 如果内容中包含参数标记，格式化显示
            if (content.content.includes('{#param')) {
              // 设置格式化后的内容并确保立即显示
              contentElement.innerHTML = content.content;
              
              // 标记为编辑状态，防止失焦时重置内容
              content.isEditing = true;
              content.editContent = content.content;
            } else {
              // 没有参数的情况下，直接显示内容
              contentElement.innerHTML = content.content || '';
            }
          }
        } else if (content.type === 'button' && content.content) {
          // 查找按钮内容元素
          const buttonIndex = selectedTemplateContents.value.indexOf(content);
          const buttonElement = document.querySelector(`.preview-button[data-content-index="${buttonIndex}"] span`);
          
          if (buttonElement) {
            // 设置按钮内容
            buttonElement.innerHTML = content.content;
          }
        }
      });
    }
    
    console.log('内容参数显示初始化完成');
  } catch (error) {
    console.error('初始化内容参数显示失败:', error);
  }
};


// 当获取到模板数据后，为每个内容项注入userId
watch(selectedTemplate, (newTemplate) => {
  if (newTemplate) {
    selectedTemplateContents.value = newTemplate.pages?.flatMap(page => page.contents || []) || [];
    // 为每个content项添加userId（从顶层模板数据获取）
    selectedTemplateContents.value.forEach(content => {
      content.userId = newTemplate.userId; // 假设顶层有userId
    });
  }
});
// 处理文档点击事件
const handleDocumentClick = (event) => {
  // 处理点击模板编辑区域外的情况
  if (selectedContent.value) {
    // 检查点击是否是在右侧设置面板之外
    const rightPanel = document.querySelector('.template-settings-panel');
    const previewArea = document.querySelector('.preview-container');
    
    if (rightPanel && !rightPanel.contains(event.target) && 
        previewArea && !previewArea.contains(event.target)) {
      // 保存当前内容设置
      saveContentSettings(selectedContent.value);
      // 清除选中内容
      selectedContent.value = null;
    }
  }
};


// 判断是否是通知类模板
const isNotificationTemplate = computed(() => {
  if (!selectedTemplate.value) return false;
  
  // 检查模板类型 - 检查 tplType 是否为 "notification"
  if (selectedTemplate.value.tplType && typeof selectedTemplate.value.tplType === 'string' && 
      selectedTemplate.value.tplType === 'notification') {
    return true;
  }
  
  // 检查cardId是否为通知类，移除templateName判断
  if (selectedTemplate.value.cardId && typeof selectedTemplate.value.cardId === 'string' && 
      selectedTemplate.value.cardId.includes('notification')) {
    return true;
  }
  
  return false;
});

// 判断是否是多图文模板
const isMultiTextTemplate = computed(() => {
  if (!selectedTemplate.value) return false;
  
  // 检查模板类型
  const templateType = templateFactory.detectTemplateType(selectedTemplate.value);
  if (templateType === 'multitext') {
    return true;
  }
  
  // 检查cardId
  if (selectedTemplate.value.cardId && typeof selectedTemplate.value.cardId === 'string' && 
      selectedTemplate.value.cardId.includes('multitext')) {
    return true;
  }
  
  return false;
});

// 判断是否是长文本模板
const isLongTextTemplate = computed(() => {
  if (!selectedTemplate.value) return false;
  
  // 检查模板类型
  const templateType = templateFactory.detectTemplateType(selectedTemplate.value);
  if (templateType === 'longtext') {
    return true;
  }
  
  // 检查cardId
  if (selectedTemplate.value.cardId && typeof selectedTemplate.value.cardId === 'string' && 
      selectedTemplate.value.cardId.includes('longtext')) {
    return true;
  }
  
  return false;
});

// 判断是否是横滑模板
const isHorizontalSwipeTemplate = computed(() => {
  if (!selectedTemplate.value) return false;
  
  // 检查模板类型
  const templateType = templateFactory.detectTemplateType(selectedTemplate.value);
  if (templateType === 'horizontalswipe') {
    return true;
  }
  
  // 检查cardId
  if (selectedTemplate.value.cardId && typeof selectedTemplate.value.cardId === 'string' && 
      selectedTemplate.value.cardId.includes('horizontalswipe')) {
    return true;
  }
  
  return false;
});

// 判断是否是券+商品模板
const isCouponProductTemplate = computed(() => {
  if (!selectedTemplate.value) return false;
  
  // 检查模板类型
  const templateType = templateFactory.detectTemplateType(selectedTemplate.value);
  if (templateType === 'couponproduct') {
    return true;
  }
  
  // 检查cardId
  if (selectedTemplate.value.cardId && typeof selectedTemplate.value.cardId === 'string' && 
      selectedTemplate.value.cardId.includes('couponproduct')) {
    return true;
  }
  
  return false;
});

// 控制通知类设置面板的显示
const showNotificationSettings = ref(false);
// 控制多图文设置面板的显示
const showMultiTextSettings = ref(false);
// 控制长文本设置面板的显示
const showLongTextSettings = ref(false);
// 控制电商设置面板的显示
const showEcommerceSettings = ref(false);
// 控制多商品设置面板的显示
const showMultiProductSettings = ref(false);
// 控制按钮设置面板的显示
const showButtonSettings = ref(false);
// 控制单卡券设置面板的显示
const showCardVoucherSettings = ref(false);
// 控制券+商品设置面板的显示
const showCouponProductSettings = ref(false);

// 券+商品设置状态
const couponProductSettingsState = ref(null);

// 通知类设置的虚拟内容对象
const notificationSettingsContent = computed(() => ({
  contentId: 'notification-settings',
  type: 'notification-settings',
  isNotificationSettings: true
}));

// 多图文设置的虚拟内容对象
const multiTextSettingsContent = computed(() => ({
  contentId: 'multitext-settings',
  type: 'multitext-settings',
  isMultiTextSettings: true
}));

// 长文本设置的虚拟内容对象
const longTextSettingsContent = computed(() => ({
  contentId: 'longtext-settings',
  type: 'longtext-settings',
  isLongTextSettings: true
}));

// 电商设置的虚拟内容对象
const ecommerceSettingsContent = computed(() => ({
  contentId: 'ecommerce-settings',
  type: 'ecommerce-settings',
  isEcommerceSettings: true,
  currentData: ecommerceSettingsState.value
}));

// 多商品设置的虚拟内容对象
const multiProductSettingsContent = computed(() => {
  console.log('TemplateEditor - 计算多商品设置内容');

  try {
    // 如果没有多商品设置状态，或者状态为null，从模板内容提取数据
    if (!multiProductSettingsState.value) {
      console.log('TemplateEditor - 没有多商品设置状态，从模板内容提取数据');
      
      // 从模板内容提取多商品数据
      const extractedData = extractMultiProductDataFromContents(selectedTemplateContents.value);
      console.log('TemplateEditor - 提取的多商品数据:', extractedData);
      
      return {
        type: 'multi-product-settings',
        contentId: 'multi-product-settings',
        isMultiProductSettings: true,
        currentData: {
          headerTitle: extractedData.headerTitle || '',
          headerSubtitle: extractedData.headerSubtitle || '',
          products: extractedData.products || [],
          selectedProductIndex: 0,
          productCount: 2, // 始终默认为2，不根据提取到的商品数量设置
          aimCurrencyDisplay: 1
        }
      };
    }
    
    console.log('TemplateEditor - 使用现有多商品设置状态:', multiProductSettingsState.value);
    console.log('multiProductSettingsContent - 返回前 currentData.products:', multiProductSettingsState.value?.products);
    return {
      type: 'multi-product-settings',
      contentId: 'multi-product-settings',
      isMultiProductSettings: true,
      currentData: multiProductSettingsState.value
    };
  } catch (error) {
    console.error('TemplateEditor - 计算多商品设置内容时出错:', error);
    return {
      type: 'multi-product-settings',
      contentId: 'multi-product-settings',
      isMultiProductSettings: true,
      currentData: {
        headerTitle: '',
        headerSubtitle: '',
        products: [],
        selectedProductIndex: 0,
        productCount: 2,
        aimCurrencyDisplay: 1
      }
    };
  }
});

// 单卡券设置的虚拟内容对象
const cardVoucherSettingsContent = computed(() => ({
  contentId: 'card-voucher-settings',
  type: 'card-voucher-settings',
  isCardVoucherSettings: true
}));

// 券+商品设置的虚拟内容对象
const couponProductSettingsContent = computed(() => {
  console.log('TemplateEditor - 计算券+商品设置内容');
  
  try {
    // 如果没有券+商品设置状态，或者状态为null，从模板内容提取数据
    if (!couponProductSettingsState.value) {
      console.log('TemplateEditor - 没有券+商品设置状态，从模板内容提取数据');
      
      // 从模板内容提取券+商品数据
      const extractedData = extractCouponProductDataFromContents(selectedTemplateContents.value);
      console.log('TemplateEditor - 提取的券+商品数据:', extractedData);
      
      return {
        type: 'coupon-product-settings',
        contentId: 'coupon-product-settings',
        isCouponProductSettings: true,
        currentData: {
          headerProduct: extractedData.headerProduct || {
            image: '',
            title: '',
            description: '',
            imageClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '', actionPath: '' }
          },
          coupon: extractedData.coupon || {
            amount: '',
            title: '',
            description: '',
            buttonText: '',
            buttonClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '', actionPath: '' },
            aimCurrencyDisplay: 1
          },
          products: extractedData.products || [],
          selectedProductIndex: 0,
          productCount: 3, // 默认为3个商品
          aimCurrencyDisplay: 1
        }
      };
    }
    
    console.log('TemplateEditor - 使用现有券+商品设置状态:', couponProductSettingsState.value);
    return {
      type: 'coupon-product-settings',
      contentId: 'coupon-product-settings',
      isCouponProductSettings: true,
      currentData: couponProductSettingsState.value
    };
  } catch (error) {
    console.error('TemplateEditor - 计算券+商品设置内容时出错:', error);
    return {
      type: 'coupon-product-settings',
      contentId: 'coupon-product-settings',
      isCouponProductSettings: true,
      currentData: {
        headerProduct: {
          image: '',
          title: '',
          description: '',
          imageClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '', actionPath: '' }
        },
        coupon: {
          amount: '',
          title: '',
          description: '',
          buttonText: '',
          buttonClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '', actionPath: '' },
          aimCurrencyDisplay: 1
        },
        products: [],
        selectedProductIndex: 0,
        productCount: 3,
        aimCurrencyDisplay: 1
      }
    };
  }
});


// 处理长文本设置变更
const handleLongTextSettingsChange = (settings) => {
  console.log('长文本设置变更:', settings);
  // 更新长文本设置
  Object.assign(longTextSettings, settings);
  
  // 如果切换到一般样式，确保有两个按钮
  if (settings.selectedStyle === 'general') {
    console.log('长文本切换到一般样式，检查按钮数量');
    const currentButtons = selectedTemplateContents.value.filter(content => content.type === 'button');
    
    if (currentButtons.length < 2) {
      console.log('长文本一般样式缺少第二个按钮，自动添加');
      
      // 获取模板处理器
      const templateType = templateFactory.detectTemplateType(selectedTemplate.value);
      const templateHandler = templateFactory.getTemplateHandler(templateType);
      
      if (templateHandler && templateHandler.ensureButtonsForGeneralStyle) {
        // 使用模板处理器确保有两个按钮
        const updatedContents = templateHandler.ensureButtonsForGeneralStyle(selectedTemplateContents.value, settings);
        selectedTemplateContents.value = updatedContents;
        console.log('已自动添加第二个按钮');
      }
    }
  }
  // 如果切换到简单样式，移除多余的按钮（只保留第一个）
  else if (settings.selectedStyle === 'simple') {
    console.log('长文本切换到简单样式，检查是否需要移除多余按钮');
    const currentButtons = selectedTemplateContents.value.filter(content => content.type === 'button');
    
    if (currentButtons.length > 1) {
      console.log('长文本简单样式有多余按钮，移除第二个及以后的按钮');
      
      // 移除第二个及以后的按钮，只保留第一个
      const updatedContents = selectedTemplateContents.value.filter(content => {
        if (content.type === 'button') {
          const buttonIndex = currentButtons.findIndex(btn => btn.contentId === content.contentId);
          return buttonIndex === 0; // 只保留第一个按钮
        }
        return true; // 非按钮内容保留
      });
      
      selectedTemplateContents.value = updatedContents;
      console.log('已移除多余的按钮，只保留第一个按钮');
      
      // 如果当前选中的是被移除的按钮，清空选择
      if (selectedContent.value && selectedContent.value.type === 'button') {
        const buttonIndex = currentButtons.findIndex(btn => btn.contentId === selectedContent.value.contentId);
        if (buttonIndex > 0) {
          console.log('当前选中的按钮被移除，清空选择');
          selectedContent.value = null;
        }
      }
    }
  }
  
  // 强制更新模板内容显示
  nextTick(() => {
    // 触发模板内容的重新计算
    const event = new CustomEvent('longtext-settings-updated', {
      detail: settings
    });
    document.dispatchEvent(event);
  });
};


// 处理单卡券设置变更

const handleCardVoucherSettingsChange = (updatedContents) => {
  console.log('TemplateEditor - 接收到单卡券设置变化:', updatedContents);
  
  if (!updatedContents || !Array.isArray(updatedContents)) {
    console.log('TemplateEditor - 单卡券设置变化：内容无效');
    return;
  }
  
  // 直接更新selectedTemplateContents，确保数据同步
  selectedTemplateContents.value = [...updatedContents];
  
  // 标记有变化
  hasChanges.value = true;
  hasEditedContent.value = true;
  
  console.log('TemplateEditor - 单卡券设置已更新，新内容:', selectedTemplateContents.value);
};


// 处理文本获取焦点
const handleTextFocus = (content) => {
  console.log('文本获取焦点:', content);
  
  // 文本获取焦点时，清空默认内容（如果是"编辑名称"或"编辑文本"）
  if (content.content === '编辑名称' || content.content === '编辑文本') {
    content.content = '';
  }
  
  // 文本获取焦点时显示文本设置面板
  showSettings.value = true;
  showNotificationSettings.value = false;
};


// 处理对话框内容区域点击
const handleDialogContentClick = (event) => {
  
  const target = event.target;
  const clickedElement = target.closest ? target : target.parentElement;
  
  // 防止重复处理
  if (clickedElement?.getAttribute('data-click-processed')) {
    console.log('已处理过的点击事件，跳过');
    return;
  }
  
  // 标记为已处理
  if (clickedElement) {
    clickedElement.setAttribute('data-click-processed', 'true');
    setTimeout(() => {
      clickedElement.removeAttribute('data-click-processed');
    }, 100);
  }

  // 检查是否点击了交互元素（输入框、按钮等）
  const interactiveSelector = `
    button, input, select, textarea, a, 
    .el-button, .el-input, .el-select, .el-radio, .el-checkbox, .el-switch,
    .el-slider, .el-date-picker, .el-time-picker, .el-cascader, .el-transfer,
    .el-upload, .el-rate, .el-color-picker, .el-form-item, .el-tabs,
    .el-menu, .el-dropdown, .el-tooltip, .el-popover,
    .editable-content, .text-content, .rich-param-input,
    [contenteditable="true"], [data-editable="true"],
    .el-input__inner, .el-input__wrapper, .el-textarea__inner,
    .setting-item-link, .setting-item, .setting-label,
    .click-event-settings, .carousel-settings, .ecommerce-settings
  `;
  
  const interactiveElement = clickedElement?.closest(interactiveSelector);
  if (interactiveElement) {
    return;
  }

  // 检查是否点击了头部区域
  const headerElement = clickedElement?.closest('.el-dialog__header');
  if (headerElement) {
    console.log('点击了头部区域，不处理，元素:', headerElement);
    return;
  }

  // 检查是否点击了左侧边栏
  const leftSectionElement = clickedElement?.closest('.left-section');
  if (leftSectionElement) {
    // 进一步检查是否点击的是真正的左侧边栏内容（模板列表、标签等）
    const isActualSidebar = clickedElement?.closest('.template-card, .tab-container, .el-radio-button, .scene-title, .template-list');
    if (isActualSidebar) {
      console.log('点击了真正的左侧边栏，不处理，元素:', leftSectionElement);
      return;
    }
    // 如果在left-section内但不是边栏内容，继续处理（可能是预览区域）
    console.log('在left-section内但不是边栏内容，继续处理，元素:', clickedElement);
  }

  // 检查是否点击了右侧设置面板
  const rightSectionElement = clickedElement?.closest('.right-section, .template-settings, .notification-settings, .click-event-settings-button, .setting-group, .setting-content');
  if (rightSectionElement) {
    console.log('点击了右侧设置面板，不处理，元素:', rightSectionElement);
    return;
  }

  // 检查是否点击了预览容器内的内容
  const previewContainer = clickedElement?.closest('.template-preview-container, .template-preview-core, .notification-template');
  if (previewContainer) {
    console.log('点击了预览容器内部，检查是否为可编辑内容，容器:', previewContainer);
    
    // 如果点击的是可编辑内容，不处理
    const editableElement = clickedElement?.closest('.editable-content, .text-content, .rich-param-input, [contenteditable="true"]');
    if (editableElement) {
      console.log('点击了可编辑内容，不处理，元素:', editableElement);
      return;
    }
    
    // 如果点击的是预览容器内的空白区域，也不处理（由PreviewContainer自己处理）
    console.log('点击了预览容器内的非可编辑区域，交由预览容器处理');
    return;
  }

  // 检查是否点击了middle-section中的空白区域（预览容器外部）
  const middleSection = clickedElement?.closest('.middle-section') || 
                       (clickedElement?.classList?.contains('middle-section') ? clickedElement : null);
  
  if (middleSection) {
    console.log('点击了middle-section中的空白区域（预览容器外部）');
    
    if (isNotificationTemplate.value) {
      // 通知类模板：显示通知设置面板
      showNotificationSettings.value = true;
      showMultiTextSettings.value = false;
      showLongTextSettings.value = false;
      showEcommerceSettings.value = false;
      showMultiProductSettings.value = false;
      showCardVoucherSettings.value = false;
      showCouponProductSettings.value = false;
      showSettings.value = false;
      selectedContent.value = notificationSettingsContent.value;
      console.log('通知类模板：显示通知设置面板');
    } else if (isMultiTextTemplate.value) {
      // 多图文模板：显示多图文设置面板
      showNotificationSettings.value = false;
      showMultiTextSettings.value = true;
      showLongTextSettings.value = false;
      showEcommerceSettings.value = false;
      showMultiProductSettings.value = false;
      showCardVoucherSettings.value = false;
      showCouponProductSettings.value = false;
      showSettings.value = false;
      selectedContent.value = multiTextSettingsContent.value;
      console.log('多图文模板：显示多图文设置面板');
    } else if (isLongTextTemplate.value) {
      // 长文本模板：显示长文本设置面板
      showNotificationSettings.value = false;
      showMultiTextSettings.value = false;
      showLongTextSettings.value = true;
      showEcommerceSettings.value = false;
      showMultiProductSettings.value = false;
      showCardVoucherSettings.value = false;
      showCouponProductSettings.value = false;
      showSettings.value = false;
      selectedContent.value = longTextSettingsContent.value;
      console.log('长文本模板：显示长文本设置面板');
    } else if (isCardVoucherTemplate.value) {
      // 单卡券模板：点击空白区域，隐藏单卡券设置面板
      showNotificationSettings.value = false;
      showMultiTextSettings.value = false;
      showLongTextSettings.value = false;
      showEcommerceSettings.value = false;
      showMultiProductSettings.value = false;
      showCardVoucherSettings.value = false;
      showCouponProductSettings.value = false;
      showSettings.value = false;
      
      // 清除选中内容
      selectedContent.value = null;
      console.log('单卡券模板：点击空白区域，隐藏单卡券设置面板');
    } else if (isMultiProductTemplate.value) {
      // 多商品模板：点击空白区域，隐藏多商品设置面板
      showNotificationSettings.value = false;
      showMultiTextSettings.value = false;
      showLongTextSettings.value = false;
      showEcommerceSettings.value = false;
      showMultiProductSettings.value = false;
      showCardVoucherSettings.value = false;
      showCouponProductSettings.value = false;
      showSettings.value = false;

      selectedContent.value = null;
      console.log('多商品模板：点击空白区域，隐藏多商品设置面板');
    } else if (isEcommerceTemplate.value) {
      // 电商模板：点击空白区域，隐藏电商设置面板
      showNotificationSettings.value = false;
      showMultiTextSettings.value = false;
      showLongTextSettings.value = false;
      showEcommerceSettings.value = false;
      showMultiProductSettings.value = false;
      showCardVoucherSettings.value = false;
      showCouponProductSettings.value = false;
      showSettings.value = false;
   
      selectedContent.value = null;
      console.log('电商模板：点击空白区域，隐藏电商设置面板');
    } else if (isCouponProductTemplate.value) {
      // 券+商品模板：点击空白区域，隐藏券+商品设置面板
      showNotificationSettings.value = false;
      showMultiTextSettings.value = false;
      showLongTextSettings.value = false;
      showEcommerceSettings.value = false;
      showMultiProductSettings.value = false;
      showCardVoucherSettings.value = false;
      showCouponProductSettings.value = false;
      showSettings.value = false;
      
      // 清除选中内容
      selectedContent.value = null;
      console.log('券+商品模板：点击空白区域，隐藏券+商品设置面板');
    } else {
      // 其他模板类型（横滑模板等）：显示常规设置面板
      showNotificationSettings.value = false;
      showMultiTextSettings.value = false;
      showLongTextSettings.value = false;
      showEcommerceSettings.value = false;
      showMultiProductSettings.value = false;
      showCardVoucherSettings.value = false;
      showCouponProductSettings.value = false;
      showSettings.value = true;
      selectedContent.value = null;
      console.log('其他模板类型：显示常规设置面板');
    }
    console.log('=== handleDialogContentClick 结束 - middle-section处理完成 ===');
    return;
  }

  // 其他区域的点击不处理
  console.log('点击了其他区域，不处理');
  console.log('=== handleDialogContentClick 结束 - 未找到匹配元素 ===');
};

// 处理参数对数量变化
const handleParamCountChange = (count) => {
  console.log('参数对数量变化:', count);
  
  // 更新通知设置中的参数对数量
  notificationSettings.maxVisibleParams = count;
  
  // 如果当前是通知类模板，动态调整内容
  if (selectedTemplate.value && selectedTemplate.value.tplType === '通知类') {
    const template = selectedTemplate.value;
    
    // 对于增强类模板，需要计算总的参数对数量（第一对固定 + 额外参数对）
    const totalParamsNeeded = template.templateName?.includes('增强') 
      ? 1 + count  // 第一对固定 + 额外参数对
      : count;     // 一般模板直接使用设置值
    
    console.log('增强类模板参数对计算:', {
      isEnhanced: template.templateName?.includes('增强'),
      extraParams: count,
      totalParams: totalParamsNeeded
    });
    
    // 确保有足够的参数对内容
    ensureParamPairs(template, totalParamsNeeded);
    
    // 触发预览更新
    nextTick(() => {
      // 强制更新预览组件
      selectedTemplateContents.value = [...selectedTemplateContents.value];
    });
  }
};

// 处理按钮数量变化
const handleButtonCountChange = (count) => {
  console.log('按钮数量变化:', count);
  // 直接更新通知设置中的按钮数量
  notificationSettings.maxVisibleButtons = count;
};

// 处理图文对数量变化
const handlePairCountChange = (count) => {
  console.log('图文对数量变化:', count);
  
  // 直接更新多图文设置中的图文对数量
  multiTextSettings.pairCount = count;
  
  // 如果当前是多图文模板，动态调整内容
  if (selectedTemplate.value && isMultiTextTemplate.value) {
    const template = selectedTemplate.value;
    const templateType = templateFactory.detectTemplateType(template);
    const templateHandler = templateFactory.getTemplateHandler(templateType);
    
    console.log('多图文模板图文对数量变化，调整内容:', {
      模板: template.templateName,
      新数量: count,
      当前内容: selectedTemplateContents.value
    });
    
    // 使用MultiTextTemplate的ensureTextImagePairs方法补齐内容
    const updatedContents = MultiTextTemplate.ensureTextImagePairs(selectedTemplateContents.value, count);
    
    // 重新分配positionNumber确保正确顺序
    const finalContents = updatedContents.map((content) => {
      if (content.type === 'image') {
        if (content.role === 'header-image') {
          content.positionNumber = 1;
        } else if (content.role === 'pair-image' && content.pairIndex) {
          content.positionNumber = (content.pairIndex - 1) * 2 + 3;
        }
      } else if (content.type === 'text') {
        if (content.role === 'header-text') {
          content.positionNumber = 2;
        } else if (content.role === 'pair-text' && content.pairIndex) {
          content.positionNumber = (content.pairIndex - 1) * 2 + 4;
        }
      }
      return content;
    });
    
    selectedTemplateContents.value = finalContents;
    
    console.log('多图文内容更新完成:', selectedTemplateContents.value);
    
    // 触发预览更新
    nextTick(() => {
      // 强制更新预览组件
      selectedTemplateContents.value = [...selectedTemplateContents.value];
    });
  }
};

// 确保有足够的参数对
const ensureParamPairs = (template, requiredCount) => {
  if (!selectedTemplateContents.value) {
    return;
  }
  
  const contents = selectedTemplateContents.value;
  const existingParams = contents.filter(c => c.type === 'text' && c.pageLines > 2);
  
  // 按pageLines分组统计现有参数对数量
  const paramPairsByLine = {};
  existingParams.forEach(param => {
    if (!paramPairsByLine[param.pageLines]) {
      paramPairsByLine[param.pageLines] = [];
    }
    paramPairsByLine[param.pageLines].push(param);
  });
  
  const currentPairCount = Object.keys(paramPairsByLine).length;
  
  // 如果需要更多参数对，添加新的
  if (currentPairCount < requiredCount) {
    // 获取所有已使用的positionNumber
    const usedPositionNumbers = new Set(
      selectedTemplateContents.value
        .filter(c => c.positionNumber !== undefined && c.positionNumber !== null)
        .map(c => c.positionNumber)
    );
    
    // 找到下一个可用的positionNumber的函数
    const getNextAvailablePositionNumber = () => {
      let nextNumber = 1;
      while (usedPositionNumbers.has(nextNumber)) {
        nextNumber++;
      }
      usedPositionNumbers.add(nextNumber);
      return nextNumber;
    };
    
    for (let i = currentPairCount; i < requiredCount; i++) {
      const lineNumber = i + 3; // 从第3行开始（标题第1行，描述第2行）
      
      // 获取左侧参数的positionNumber
      const leftPositionNumber = getNextAvailablePositionNumber();
      
      // 获取右侧参数的positionNumber
      const rightPositionNumber = getNextAvailablePositionNumber();
      
      // 添加左侧参数
      selectedTemplateContents.value.push({
        contentId: `param-left-${i + 1}`,
        type: 'text',
        content: `编辑名称`,
        pageId: 1,
        pageLines: lineNumber,
        pageLayout: 'left',
        isTextTitle: 0,
        positionNumber: leftPositionNumber
      });
      
      // 添加右侧参数
      selectedTemplateContents.value.push({
        contentId: `param-right-${i + 1}`,
        type: 'text',
        content: `编辑文本`,
        pageId: 1,
        pageLines: lineNumber,
        pageLayout: 'right',
        isTextTitle: 0,
        positionNumber: rightPositionNumber
      });
    }
  } else if (currentPairCount > requiredCount) {
    // 如果当前参数对数量超过需要的数量，移除多余的参数对
    const linesToRemove = [];
    const sortedLines = Object.keys(paramPairsByLine).map(Number).sort((a, b) => b - a); // 从大到小排序
    
    for (let i = 0; i < currentPairCount - requiredCount; i++) {
      linesToRemove.push(sortedLines[i]);
    }
    
    // 移除多余的参数对
    selectedTemplateContents.value = selectedTemplateContents.value.filter(content => {
      if (content.type === 'text' && content.pageLines > 2) {
        return !linesToRemove.includes(content.pageLines);
      }
      return true;
    });
  }
};

// 确保有足够的按钮
const ensureButtons = (template, requiredCount) => {
  if (!selectedTemplateContents.value) {
    return;
  }
  
  const contents = selectedTemplateContents.value;
  const existingButtons = contents.filter(c => c.type === 'button');
  
  // 如果需要更多按钮，添加新的
  if (existingButtons.length < requiredCount) {
    // 获取所有已使用的positionNumber
    const usedPositionNumbers = new Set(
      selectedTemplateContents.value
        .filter(c => c.positionNumber !== undefined && c.positionNumber !== null)
        .map(c => c.positionNumber)
    );
    
    // 找到下一个可用的positionNumber的函数
    const getNextAvailablePositionNumber = () => {
      let nextNumber = 1;
      while (usedPositionNumbers.has(nextNumber)) {
        nextNumber++;
      }
      usedPositionNumbers.add(nextNumber);
      return nextNumber;
    };
    
    for (let i = existingButtons.length; i < requiredCount; i++) {
      const buttonPositionNumber = getNextAvailablePositionNumber();
      
      selectedTemplateContents.value.push({
        contentId: `button-${i + 1}`,
        type: 'button',
        content: `编辑按钮`,
        pageId: 1,
        pageLines: 7, // 按钮通常在第7行
        positionNumber: buttonPositionNumber
      });
    }
  } else if (existingButtons.length > requiredCount) {
    // 如果当前按钮数量超过需要的数量，移除多余的按钮
    const buttonsToKeep = existingButtons.slice(0, requiredCount);
    const buttonIdsToKeep = buttonsToKeep.map(btn => btn.contentId);
    
    // 移除多余的按钮
    selectedTemplateContents.value = selectedTemplateContents.value.filter(content => {
      if (content.type === 'button') {
        return buttonIdsToKeep.includes(content.contentId);
      }
      return true;
    });
  }
};


// 暴露方法给模板使用
defineExpose({
  open,
  openEdit,
  openCopy,
  close,
  editTemplate: handleEditTemplate,
  submitTemplate,
  handleTextFocus,
  handleParamCountChange,
  handleButtonCountChange
});

// 控制常规设置面板的显示
const showSettings = ref(false);

// 判断是否是电商模板
const isEcommerceTemplate = computed(() => {
  if (!selectedTemplate.value) return false;
  
  // 使用TemplateFactory的精确检测方法
  const templateType = templateFactory.detectTemplateType(selectedTemplate.value);
  if (templateType === 'ecommerce') {
    return true;
  }
  
  // 检查模板类型 - 检查 tplType 是否为 "ecommerce"
  if (selectedTemplate.value.tplType && typeof selectedTemplate.value.tplType === 'string' && 
      selectedTemplate.value.tplType === 'ecommerce') {
    return true;
  }
  
  return false;
});

// 判断是否是多商品模板
const isMultiProductTemplate = computed(() => {
  if (!selectedTemplate.value) return false;
  
  // 使用TemplateFactory的精确检测方法
  const templateType = templateFactory.detectTemplateType(selectedTemplate.value);
  if (templateType === 'multiproduct') { // 修复大小写问题
    return true;
  }
  
  // 检查模板类型 - 检查 tplType 是否为 "multiProduct" 或 "multiproduct"
  if (selectedTemplate.value.tplType && typeof selectedTemplate.value.tplType === 'string' && 
      (selectedTemplate.value.tplType === 'multiProduct' || selectedTemplate.value.tplType === 'multiproduct')) {
    return true;
  }
  
  // 检查 cardId 是否包含多商品标识
  if (selectedTemplate.value.cardId && (selectedTemplate.value.cardId === 'com.hbm.ecommerce')) {
    return true;
  }
  return false;
});

// 判断是否是单卡券模板
const isCardVoucherTemplate = computed(() => {
  if (!selectedTemplate.value) return false;
  
  // 使用TemplateFactory的精确检测方法
  const templateType = templateFactory.detectTemplateType(selectedTemplate.value);
  if (templateType === 'cardvoucher') {
    return true;
  }
  
  // 检查cardId
  if (selectedTemplate.value.cardId === 'com.hbm.cardVoucher') {
    return true;
  }
  
  return false;
});

// 添加独立的电商设置状态管理
const ecommerceSettingsState = ref(null);

// 添加多商品设置状态管理
const multiProductSettingsState = ref(null);

// 获取电商展示数据
const getEcommerceDisplayData = () => {
  console.log('TemplateEditor - 获取电商展示数据');
  
  if (selectedTemplate.value?.cardId === 'com.hbm.ecImageAndText') {
    // 返回电商设置状态数据
    console.log('TemplateEditor - 使用电商设置状态数据:', ecommerceSettingsState.value);
    return ecommerceSettingsState.value;
  }
  
  console.log('TemplateEditor - 非电商模板，返回null');
  return null;
};

// 获取多商品展示数据
const getMultiProductDisplayData = () => {
  console.log('TemplateEditor - 计算多商品显示数据');
  
  // 如果有多商品设置状态，使用设置状态的数据
  if (multiProductSettingsState.value) {
    console.log('TemplateEditor - 使用多商品设置状态数据:', multiProductSettingsState.value);
    return multiProductSettingsState.value;
  }
  
  // 否则从模板内容提取数据
  const extractedData = extractMultiProductDataFromContents(selectedTemplateContents.value);
  console.log('TemplateEditor - 从模板内容提取多商品显示数据:', extractedData);
  
  return extractedData;
};

// 券+商品显示数据计算
const couponProductDisplayData = computed(() => {
  if (!isCouponProductTemplate.value) return null;
  
  console.log('TemplateEditor - 计算券+商品显示数据');
  
  // 如果有券+商品设置状态，使用设置状态的数据
  if (couponProductSettingsState.value) {
    console.log('TemplateEditor - 使用券+商品设置状态数据:', couponProductSettingsState.value);
    return couponProductSettingsState.value;
  }
  
  // 否则从模板内容提取数据
  const extractedData = extractCouponProductDataFromContents(selectedTemplateContents.value);
  console.log('TemplateEditor - 从模板内容提取券+商品显示数据:', extractedData);
  
  return extractedData;
});

// 初始化多商品设置状态
const initializeMultiProductSettingsState = (extractedData) => {
  console.log('TemplateEditor - 初始化多商品设置状态，提取的数据:', extractedData);
  
  // 使用API数据初始化多商品设置状态，深拷贝products避免污染
  multiProductSettingsState.value = cloneDeep({
    headerTitle: extractedData.headerTitle || '精选商品',
    headerSubtitle: extractedData.headerSubtitle || '为您推荐',
    productCount: 2, // 默认显示2个商品
    selectedProductIndex: 0,
    products: (extractedData.products || []).map((product, index) => ({
      id: product.id || index + 1,
      image: product.image || '',
      content: product.content,
      title: product.title || '',
      tag: product.tag || '',
      price: product.price || '',
      buttonText: product.buttonText || '立即购买',
      hidden: index >= 2, // 默认第3个商品隐藏
      visible: index < 2, // 前2个商品可见
      positionNumber: index + 1,
      aimCurrencyDisplay: 1, // 默认显示￥符号
      imageClickEvent: product.imageClickEvent || {
        type: 'OPEN_BROWSER',
        url: '',
      },
      buttonClickEvent: product.buttonClickEvent || {
        type: 'OPEN_BROWSER',
        url: '',
      }
    })),
    aimCurrencyDisplay: null // 默认不显示￥符号
  });
  
  console.log('TemplateEditor - 多商品设置状态初始化完成:', multiProductSettingsState.value);
  console.log('initializeMultiProductSettingsState - products:', multiProductSettingsState.value.products);
};

// 处理多商品设置变化
const handleMultiProductSettingsChange = (updatedContent) => {
  console.log('TemplateEditor - 多商品设置变化:', updatedContent);
  
  try {
    if (!updatedContent || updatedContent.type !== 'multi-product-settings' || !updatedContent.isMultiProductSettings) {
      console.warn('TemplateEditor - 无效的多商品设置内容');
      return;
    }
    
    const currentData = updatedContent.currentData;
    if (!currentData) {
      console.warn('TemplateEditor - 缺少currentData');
      return;
    }
    
    console.log('TemplateEditor - 更新多商品设置状态:', currentData);
    
    // 更新多商品设置状态，深拷贝
    multiProductSettingsState.value = cloneDeep({
      ...currentData
    });
    
    // 同步数据到模板内容
    console.log('TemplateEditor - 开始同步数据到模板内容');
    
    // 更新主标题 (pageLines: 1)
    const headerTitleContent = selectedTemplateContents.value.find(item => 
      item.pageLines === 1 && item.type === 'text'
    );
    if (headerTitleContent && currentData.headerTitle) {
      console.log('TemplateEditor - 更新主标题:', currentData.headerTitle);
      headerTitleContent.content = currentData.headerTitle;
    }
    
    // 更新副标题 (pageLines: 2)
    const headerSubtitleContent = selectedTemplateContents.value.find(item => 
      item.pageLines === 2 && item.type === 'text'
    );
    if (headerSubtitleContent && currentData.headerSubtitle) {
      console.log('TemplateEditor - 更新副标题:', currentData.headerSubtitle);
      headerSubtitleContent.content = currentData.headerSubtitle;
    }
    
    // 更新商品数据
    if (currentData.products && Array.isArray(currentData.products)) {
      currentData.products.forEach((product, productIndex) => {
        if (productIndex >= 3) return; // 最多3个商品
        
        const pageLine = productIndex + 3; // 商品数据从第3行开始
        console.log(`TemplateEditor - 更新第${productIndex + 1}个商品 (pageLine: ${pageLine}):`, product);
        
        // 根据用户设置的商品数量控制商品显示/隐藏
        const shouldShow = productIndex < (currentData.productCount || 2);
        // 获取当前商品行的所有内容
        const productContents = selectedTemplateContents.value.filter(item => 
          item.pageLines === pageLine
        );
        
        if (productContents.length > 0) {

          // 更新价格时确保传递正确的aimCurrencyDisplay值
          const priceContent = productContents.find(item => 
            item.type === 'text' && item.pageLayout === 'right_bottom_left'
          );
          if (priceContent && product.price !== undefined) {
            priceContent.content = product.price;
            // 根据用户设置的货币开关状态设置aimCurrencyDisplay
            priceContent.aimCurrencyDisplay = product.aimCurrencyDisplay !== undefined ? product.aimCurrencyDisplay : 1;
          }
          
          // 设置所有商品内容的可见性
          productContents.forEach(content => {
            content.visible = shouldShow;
          });
          
          // 更新图片
          const imageContent = productContents.find(item => 
            item.type === 'image' && item.pageLayout === 'left'
          );
          if (imageContent && product.image) {
            console.log(`TemplateEditor - 更新商品${productIndex + 1}图片:`, product.image);
            imageContent.src = product.image;
            imageContent.content = product.content;
          }
          
          // 更新标题
          const titleContent = productContents.find(item => 
            item.type === 'text' && item.pageLayout === 'right_top'
          );
          if (titleContent && product.title !== undefined) {
            console.log(`TemplateEditor - 更新商品${productIndex + 1}标题:`, product.title);
            titleContent.content = product.title;
          }
          
          // 更新标签
          const tagContent = productContents.find(item => 
            item.type === 'text' && item.pageLayout === 'right_center'
          );
          if (tagContent && product.tag !== undefined) {
            console.log(`TemplateEditor - 更新商品${productIndex + 1}标签:`, product.tag);
            tagContent.content = product.tag;
          }
          
          // 更新按钮
          const buttonContent = productContents.find(item => 
            item.type === 'button' && item.pageLayout === 'right_bottom_right'
          );
          if (buttonContent && product.buttonText !== undefined) {
            console.log(`TemplateEditor - 更新商品${productIndex + 1}按钮:`, product.buttonText);
            buttonContent.content = product.buttonText;
            
            // 更新按钮点击事件和actionJson
            if (product.buttonClickEvent && product.actionJson) {
              const clickEvent = product.buttonClickEvent;

              // 设置actionJson
              buttonContent.actionJson = product.actionJson;

              // 根据点击事件类型设置actionType
              // 优先使用 clickEvent.actionType，如果没有则使用 clickEvent.type
              buttonContent.actionType = clickEvent.actionType || ClickEventTypeConverter.toActionType(clickEvent.type);

              console.log(`TemplateEditor - 更新商品${productIndex + 1}按钮点击事件:`, {
                actionType: buttonContent.actionType,
                actionJson: buttonContent.actionJson
              });
            } else if (product.buttonClickEvent) {
              // 如果没有actionJson但有clickEvent，生成actionJson
              const clickEvent = product.buttonClickEvent;

              // 使用统一的方法处理点击事件
              buttonContent.actionType = ClickEventTypeConverter.toActionType(clickEvent.type);
              buttonContent.actionJson = ActionJsonGenerator.fromClickEventSettings(clickEvent);

              console.log(`TemplateEditor - 生成商品${productIndex + 1}按钮actionJson:`, {
                actionType: buttonContent.actionType,
                actionJson: buttonContent.actionJson
              });
            }
          }
        }
      });
    }
    
    // 设置变更标志
    hasChanges.value = true;
    hasEditedContent.value = true;
    
    console.log('TemplateEditor - 多商品设置同步完成');
    
  } catch (error) {
    console.error('TemplateEditor - 处理多商品设置变化时出错:', error);
  }
};

//电商提交
const processEcommerceTemplateData = () => {
  return EcommerceTemplate.processEcommerceTemplateData(ecommerceSettingsState.value);
};


// 处理电商设置变化
const handleEcommerceSettingsChange = (newContent) => {
  console.log('TemplateEditor - 处理电商设置变化:', newContent);
  
  try {
    // 检查是否是电商设置类型
    if (newContent.type !== 'ecommerce-settings' || !newContent.isEcommerceSettings) {
      console.warn('TemplateEditor - 不是电商设置类型的内容更新');
      return;
    }
    
    // 获取新的数据
    const newData = newContent.currentData;
    if (!newData) {
      console.warn('TemplateEditor - 电商设置更新中没有 currentData');
      return;
    }
    
    // 更新电商设置状态
    ecommerceSettingsState.value = {
      ...ecommerceSettingsState.value,
      ...newData
    };
    
    console.log('TemplateEditor - 电商设置状态已更新:', ecommerceSettingsState.value);
    
    // 标记内容已修改
    hasChanges.value = true;
    hasEditedContent.value = true;
    
    // 如果有图片数据变化，更新对应的内容项
    if (newData.images && Array.isArray(newData.images)) {
      // 查找电商图片内容项并更新
      const imageContentIndex = selectedTemplateContents.value.findIndex(
        item => item.type === 'image' && (item.templateType === 'ecommerce' || item.isEcommerceImage)
      );
      
      if (imageContentIndex !== -1) {
        selectedTemplateContents.value[imageContentIndex] = {
          ...selectedTemplateContents.value[imageContentIndex],
          carouselImages: newData.images,
          currentImageIndex: newData.currentImageIndex || 0,
          currentData: newData
        };
        
        console.log('TemplateEditor - 更新了电商图片内容项');
      }
    }
    
    // 如果有按钮数据变化，更新对应的内容项
    if (newData.buttons && Array.isArray(newData.buttons)) {
      newData.buttons.forEach((button, index) => {
        // 添加安全检查，避免undefined的contentId
        if (!button || button.contentId === undefined || button.contentId === null) {
          console.warn('TemplateEditor - 跳过无效的按钮数据:', button);
          return;
        }
        
        const buttonContentIndex = selectedTemplateContents.value.findIndex(
          item => item.type === 'button' && (
            item.contentId === button.contentId || 
            item.contentId === String(button.contentId)
          )
        );
        
        if (buttonContentIndex !== -1) {
          selectedTemplateContents.value[buttonContentIndex] = {
            ...selectedTemplateContents.value[buttonContentIndex],
            content: button.text,
            hidden: button.hidden,
            clickEvent: button.clickEvent
          };
        }
      });
      
      console.log('TemplateEditor - 更新了电商按钮内容项');
    }
    
    // 更新文本内容项
    const textFields = ['priceText', 'tagText', 'titleText', 'contentText'];
    textFields.forEach(field => {
      if (newData[field] !== undefined) {
        // 查找对应的文本内容项并更新
        const textContentIndex = selectedTemplateContents.value.findIndex(item => {
          if (item.type !== 'text') return false;
          
          // 根据字段类型匹配对应的内容项
          if (field === 'priceText' && (item.contentId === 49 || (item.aimCurrencyDisplay === 1 && item.isTextTitle === 1))) {
            return true;
          } else if (field === 'tagText' && (item.contentId === 174 || (item.positionNumber === 3 && item.isTextTitle === 1))) {
            return true;
          } else if (field === 'titleText' && (item.contentId === 50 || (item.positionNumber === 4 && item.isTextTitle === 1))) {
            return true;
          } else if (field === 'contentText' && (item.contentId === 51 || (item.positionNumber === 5 && item.isTextTitle === 0))) {
            return true;
          }
          
          return false;
        });
        
        if (textContentIndex !== -1) {
          selectedTemplateContents.value[textContentIndex] = {
            ...selectedTemplateContents.value[textContentIndex],
            content: newData[field]
          };
        }
      }
    });
    
    console.log('TemplateEditor - 电商设置变化处理完成');
    
  } catch (error) {
    console.error('TemplateEditor - 处理电商设置变化时出错:', error);
  }
};

// 处理横滑设置变更
const handleHorizontalSwipeSettingsChange = (settings) => {
  console.log('=== TemplateEditor.handleHorizontalSwipeSettingsChange 开始 ===');
  console.log('TemplateEditor - 收到横滑设置变更:', settings);
  
  if (!selectedTemplate.value || !selectedTemplate.value.pages || !settings || !settings.cards) {
    console.log('TemplateEditor - 横滑设置变更：缺少必要数据，跳过保存');
    return;
  }
  
  try {
    // 直接整体替换 pages，保证结构和接口一致
    const newPages = settings.cards.map(card => ({
      pageId: card.pageId,
      contents: Array.isArray(card.contents) ? card.contents.map(item => ({ ...item })) : [],
      clickEvent: card.clickEvent ? { ...card.clickEvent } : undefined,
      actionJson: card.actionJson
    }));

    console.log('TemplateEditor - 更新前的pages:', selectedTemplate.value.pages);
    console.log('TemplateEditor - 更新后的newPages:', newPages);
    console.log('TemplateEditor - 检查clickEvent字段:', newPages.map((page, index) => ({ index, hasClickEvent: !!page.clickEvent, clickEvent: page.clickEvent })));
    selectedTemplate.value.pages = JSON.stringify(newPages);
    
    // 重新初始化selectedTemplateContents以触发预览区域更新
    if (templateFactory.isHorizontalSwipeTemplate(selectedTemplate.value)) {
      const templateHandler = templateFactory.getTemplateHandler('horizontalswipe');
      if (templateHandler && templateHandler.initializeContents) {
        const updatedContents = templateHandler.initializeContents(selectedTemplate.value);
        selectedTemplateContents.value = updatedContents.map(content => ({
          ...content,
          contentId: content.contentId || generateUniqueId(),
          userId: selectedTemplate.value.userId
        }));
        console.log('TemplateEditor - 横滑设置变更后selectedTemplateContents已更新');
      }
    }
    
    hasChanges.value = true;
    console.log('TemplateEditor - 横滑设置变更处理完成');
  } catch (error) {
    console.error('TemplateEditor - 处理横滑设置变更失败:', error);
  }
};

// 处理横滑卡片图片更新
const handleUpdateCardImage = (imageUpdateData) => {
  console.log('TemplateEditor - 处理卡片图片更新:', imageUpdateData);
  
  if (!selectedTemplate.value || !selectedTemplate.value.pages) return;
  
  const { cardIndex, imageData } = imageUpdateData;
  
  // 解析pages数据
  let pages = parsePages(selectedTemplate.value.pages);
  
  if (cardIndex >= 0 && cardIndex < pages.length) {
    const page = pages[cardIndex];
    
    // 查找页面中的图片内容 - 同时支持contents和content两种结构
    const pageContents = page.contents || page.content || [];
    if (pageContents.length > 0) {
      const imageContent = pageContents.find(content => content.type === 'image');
      if (imageContent) {
        // 更新图片路径
        imageContent.src = imageData.src;
        imageContent.alt = imageData.alt || imageContent.alt;
        
        console.log('TemplateEditor - 图片内容已更新:', imageContent);
        
        // 更新模板的pages数据
        selectedTemplate.value.pages = JSON.stringify(pages);
        
        // 重新初始化selectedTemplateContents以触发预览区域更新
        if (templateFactory.isHorizontalSwipeTemplate(selectedTemplate.value)) {
          const templateHandler = templateFactory.getTemplateHandler('horizontalswipe');
          if (templateHandler && templateHandler.initializeContents) {
            const updatedContents = templateHandler.initializeContents(selectedTemplate.value);
            selectedTemplateContents.value = updatedContents.map(content => ({
              ...content,
              contentId: content.contentId || generateUniqueId(),
              userId: selectedTemplate.value.userId
            }));
            console.log('TemplateEditor - selectedTemplateContents已更新:', selectedTemplateContents.value);
          }
        }
        
        // 标记有变化
        hasChanges.value = true;
      } else {
        console.error('TemplateEditor - 未找到图片内容:', pageContents);
      }
    } else {
      console.error('TemplateEditor - 页面没有内容数组:', page);
    }
  } else {
    console.error('TemplateEditor - 卡片索引超出范围:', cardIndex, '页面数量:', pages.length);
  }
};

// 处理横滑卡片内容更新
const handleUpdateCardContent = (contentUpdateData) => {
  console.log('TemplateEditor - 处理卡片内容更新:', contentUpdateData);
  
  if (!selectedTemplate.value || !selectedTemplate.value.pages) return;
  
  const { cardIndex, contentData } = contentUpdateData;
  
  // 解析pages数据
  let pages = parsePages(selectedTemplate.value.pages);
  
  if (cardIndex >= 0 && cardIndex < pages.length) {
    // 保留所有字段，尤其是 clickEvent
    pages[cardIndex] = {
      pageId: contentData.pageId,
      contents: Array.isArray(contentData.contents) ? contentData.contents.map(item => ({ ...item })) : [],
      clickEvent: contentData.clickEvent ? { ...contentData.clickEvent } : undefined
    };
      selectedTemplate.value.pages = JSON.stringify(pages);
      
      // 重新初始化selectedTemplateContents以触发预览区域更新
      if (templateFactory.isHorizontalSwipeTemplate(selectedTemplate.value)) {
        const templateHandler = templateFactory.getTemplateHandler('horizontalswipe');
        if (templateHandler && templateHandler.initializeContents) {
          const updatedContents = templateHandler.initializeContents(selectedTemplate.value);
          selectedTemplateContents.value = updatedContents.map(content => ({
            ...content,
            contentId: content.contentId || `content_${Date.now()}_${Math.random()}`,
            userId: selectedTemplate.value.userId
          }));
          console.log('TemplateEditor - selectedTemplateContents已更新:', selectedTemplateContents.value);
        }
    }
  }
};

// 处理添加横滑卡片
const handleAddCard = (cardData) => {
  console.log('TemplateEditor - 处理添加卡片:', cardData);
  
  if (!selectedTemplate.value || !selectedTemplate.value.pages) return;
  
  const { cardIndex, defaultCard } = cardData;
  
  // 解析pages数据
  let pages = parsePages(selectedTemplate.value.pages);
  
  pages.push({
    pageId: defaultCard.pageId,
    contents: Array.isArray(defaultCard.contents) ? defaultCard.contents.map(item => ({ ...item })) : []
  });
  
  // 更新模板的pages数据
  selectedTemplate.value.pages = JSON.stringify(pages);
  
  // 如果是横滑模板，更新selectedTemplateContents
  if (selectedTemplate.value) {
    const templateHandler = templateFactory.getTemplateHandler('horizontalswipe');
    if (templateHandler && templateHandler.initializeContents) {
      try {
        const contents = templateHandler.initializeContents(selectedTemplate.value);
        selectedTemplateContents.value = contents.map(content => ({
          ...content,
          contentId: content.contentId || `content_${Date.now()}_${Math.random()}`,
          userId: selectedTemplate.value.userId
        }));
        
        console.log('TemplateEditor - 添加卡片后更新selectedTemplateContents:', selectedTemplateContents.value);
      } catch (error) {
        console.error('TemplateEditor - 添加卡片后更新内容失败:', error);
      }
    }
  }
  
  // 标记有变化
  hasChanges.value = true;
  
  console.log('TemplateEditor - 新卡片已添加，索引:', cardData.cardIndex);
};

// 处理删除横滑卡片
const handleRemoveCard = (cardData) => {
  console.log('TemplateEditor - 处理删除卡片:', cardData);
  
  if (!selectedTemplate.value || !selectedTemplate.value.pages) return;
  
  const { cardIndex } = cardData;
  
  // 解析pages数据
  let pages = parsePages(selectedTemplate.value.pages);
  
  if (cardIndex >= 0 && cardIndex < pages.length && pages.length > 2) {
    // 删除指定页面
    pages.splice(cardIndex, 1);
    
    // 重新分配pageIndex
    pages.forEach((page, index) => {
      page.pageIndex = index;
    });
    
    // 更新模板的pages数据
    selectedTemplate.value.pages = JSON.stringify(pages);
    
    // 如果是横滑模板，更新selectedTemplateContents
    if (selectedTemplate.value) {
      const templateHandler = templateFactory.getTemplateHandler('horizontalswipe');
      if (templateHandler && templateHandler.initializeContents) {
        try {
          const contents = templateHandler.initializeContents(selectedTemplate.value);
          selectedTemplateContents.value = cloneDeep(contents).map(content => ({
            ...content,
            contentId: content.contentId || `content_${Date.now()}_${Math.random()}`,
            userId: selectedTemplate.value.userId
          }));
          
          console.log('TemplateEditor - 删除卡片后更新selectedTemplateContents:', selectedTemplateContents.value);
        } catch (error) {
          console.error('TemplateEditor - 删除卡片后更新内容失败:', error);
        }
      }
    }
    
    // 标记有变化
    hasChanges.value = true;
    
    console.log('TemplateEditor - 卡片已删除，索引:', cardIndex);
  }
};

// 处理卡片选择
const handleCardSelected = (cardData) => {
  // 检查当前选中的内容是否是横滑设置
  if (selectedContent.value && selectedContent.value.type === 'horizontalSwipeSettings') {
    
    if (cardData.isNewCard) {
      // 创建新的selectedContent对象，确保触发响应式更新和滚动
      selectedContent.value = {
        type: 'horizontalSwipeSettings',
        contentId: 'horizontalswipe-settings',
        content: '横滑设置',
        positionNumber: 0,
        isTextTitle: false,
        isHorizontalSwipeSettings: true,
        selectedCardIndex: cardData.selectedCardIndex,
        isNewCard: true  // 保持新增卡片标记
      };
    } else if (cardData.fromCardRemoval) {
      // 来自删除卡片的操作，需要创建新对象确保触发watcher
      selectedContent.value = {
        type: 'horizontalSwipeSettings',
        contentId: 'horizontalswipe-settings',
        content: '横滑设置',
        positionNumber: 0,
        isTextTitle: false,
        isHorizontalSwipeSettings: true,
        selectedCardIndex: cardData.selectedCardIndex,
        fromCardRemoval: true,  // 保持删除卡片标记
        timestamp: Date.now()  // 添加时间戳确保对象是新的
      };
      console.log('删除卡片操作处理完成，新的selectedContent:', selectedContent.value);
    } else if (cardData.fromSettingsPanel || cardData.forceScroll) {
      console.log('检测到来自设置面板的选择或强制滚动，创建新对象触发更新');
      // 来自设置面板的选择，需要创建新对象确保触发watcher
      selectedContent.value = {
        type: 'horizontalSwipeSettings',
        contentId: 'horizontalswipe-settings',
        content: '横滑设置',
        positionNumber: 0,
        isTextTitle: false,
        isHorizontalSwipeSettings: true,
        selectedCardIndex: cardData.selectedCardIndex,
        fromSettingsPanel: true,  // 保持设置面板标记
        timestamp: Date.now()  // 添加时间戳确保对象是新的
      };
      console.log('设置面板选择处理完成，新的selectedContent:', selectedContent.value);
    } else {
      console.log('普通卡片选择，直接更新selectedCardIndex');
      // 直接更新现有的selectedContent对象
      selectedContent.value.selectedCardIndex = cardData.selectedCardIndex;
      console.log('普通卡片选择处理完成，更新后的selectedContent:', selectedContent.value);
    }
    
    console.log('TemplateEditor - 更新横滑设置的选中卡片索引:', cardData.selectedCardIndex);
  } else {
    console.log('当前选中的不是横滑设置，跳过处理');
  }
  
  console.log('=== TemplateEditor - handleCardSelected 结束 ===');
};


// 从模板内容中提取数据
const extractMultiProductDataFromContents = (contents) => {
  console.log('TemplateEditor - 开始从模板内容提取多商品数据, contents:', contents);
  console.log('selectedTemplateContents.value:', selectedTemplateContents.value);
  if (!contents || !Array.isArray(contents) || contents.length === 0) {
    console.log('TemplateEditor - 内容为空或不是数组，使用MultiProductTemplate提供默认数据');
    // 当没有内容时，让MultiProductTemplate提供默认数据
    const templateHandler = templateFactory.getTemplateHandler('multiproduct');
    if (templateHandler && templateHandler.createDefaultContents) {
      const defaultContents = templateHandler.createDefaultContents();
      return extractMultiProductDataFromContents(defaultContents);
    }
    return {
      headerTitle: '精选商品',
      headerSubtitle: '编辑文本，最多12个中文字',
      products: [
        {
          id: 1,
          image: '',
          title: '编辑文本，最多24个中文字',
          tag: '编辑文本',
          price: '100.00',
          buttonText: '立即购买',
          aimCurrencyDisplay: 1,
          imageClickEvent: { type: 'OPEN_BROWSER', url: '' },
          buttonClickEvent: { type: 'OPEN_BROWSER', url: '' }
        },
        {
          id: 2,
          image: '',
          title: '编辑文本，最多24个中文字',
          tag: '编辑文本',
          price: '100.00',
          buttonText: '立即购买',
          aimCurrencyDisplay: 1,
          imageClickEvent: { type: 'OPEN_BROWSER', url: '' },
          buttonClickEvent: { type: 'OPEN_BROWSER', url: '' }
        }
      ],
      productCount: 2,
      selectedProductIndex: 0
    };
  }

  try {
    // 创建positionNumber到内容的映射
    const contentMap = {};
    contents.forEach(item => {
      if (item && item.positionNumber) {
        contentMap[item.positionNumber] = item;
        console.log(`映射 positionNumber ${item.positionNumber}:`, item);
      }
    });

    // 提取主标题和副标题
    // 根据MultiProductTemplate.js的generateSubmitData方法，正确的映射是：
    // positionNumber 1: 主标题 (isTextTitle: 1)
    // positionNumber 2: 副标题 (isTextTitle: 0)
    let headerTitle = '';
    let headerSubtitle = '';

    // 查找主标题 - positionNumber 1
    if (contentMap[1] && contentMap[1].type === 'text') {
      headerTitle = contentMap[1].content || '';
    }

    // 查找副标题 - positionNumber 2
    if (contentMap[2] && contentMap[2].type === 'text') {
      headerSubtitle = contentMap[2].content || '';
    }

    console.log('TemplateEditor - 找到的主标题:', headerTitle);
    console.log('TemplateEditor - 找到的副标题:', headerSubtitle);
    
    // 处理商品数据 - 根据新的数据结构
    const products = [];
    const productGroups = [
      { image: 4, title: 7, tag: 8, currency: 10, price: 11, buttonText: 12, buttonClick: 13 },
      { image: 14, title: 17, tag: 18, currency: 20, price: 21, buttonText: 22, buttonClick: 23 },
      { image: 24, title: 27, tag: 28, currency: 30, price: 31, buttonText: 32, buttonClick: 33 }
    ];

    productGroups.forEach((positions, index) => {
      const product = {
        id: index + 1,
        image: '',
        content: '',
        title: '',
        tag: '',
        price: '',
        buttonText: '',
        aimCurrencyDisplay: 1,
        buttonClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' },
        hidden: index >= 2 // 默认只显示前2个商品
      };

      // 商品图片
      if (contentMap[positions.image] && contentMap[positions.image].type === 'image') {
        product.image = contentMap[positions.image].src || contentMap[positions.image].image || contentMap[positions.image].content || '';
        product.content = contentMap[positions.image].content || '';
        console.log('extractMultiProductDataFromContents - 图片内容：', contentMap[positions.image]);
        console.log(`商品${index + 1}图片:`, product.image);
      }

      // 商品标题
      if (contentMap[positions.title] && contentMap[positions.title].type === 'text') {
        product.title = contentMap[positions.title].content || '';
        console.log(`商品${index + 1}标题:`, product.title);
      }

      // 商品标签
      if (contentMap[positions.tag] && contentMap[positions.tag].type === 'text') {
        product.tag = contentMap[positions.tag].content || '';
        console.log(`商品${index + 1}标签:`, product.tag);
      }

      // 货币符号显示控制 - 使用 currency 位置的 visible 属性
      if (contentMap[positions.currency] && contentMap[positions.currency].type === 'text') {
        product.aimCurrencyDisplay = contentMap[positions.currency].visible !== undefined ? contentMap[positions.currency].visible : 1;
        console.log(`商品${index + 1}货币符号显示:`, product.aimCurrencyDisplay);
      }

      // 商品价格 - 使用 price 位置的实际价格内容
      if (contentMap[positions.price] && contentMap[positions.price].type === 'text') {
        product.price = contentMap[positions.price].content || '';
        console.log(`商品${index + 1}价格:`, product.price);
      }

      // 按钮文本
      if (contentMap[positions.buttonText] && contentMap[positions.buttonText].type === 'text') {
        product.buttonText = contentMap[positions.buttonText].content || '立即购买';
        console.log(`商品${index + 1}按钮文本:`, product.buttonText);
      }

      // 按钮点击事件
      if (contentMap[positions.buttonClick] && contentMap[positions.buttonClick].type === 'button') {
        const actionType = contentMap[positions.buttonClick].actionType || 'OPEN_BROWSER';
        const actionJson = contentMap[positions.buttonClick].actionJson || { target: '' };
        
        // 使用统一的方法将actionType和actionJson转换为clickEvent格式
        const clickEvent = ActionJsonGenerator.toClickEvent(actionType, actionJson);
        
        product.buttonClickEvent = ActionJsonGenerator.toClickEventSettings(actionType, actionJson);
        product.actionJson = actionJson;
        console.log(`商品${index + 1}按钮点击事件:`, product.buttonClickEvent);
      }

      // 只有当商品有内容时才添加
      if (product.image || product.title || product.tag || product.price || product.buttonText || product.content) {
        products.push(product);
        console.log(`添加商品${index + 1}完整数据:`, {
          id: product.id,
          image: product.image,
          content: product.content, // 添加content字段到调试日志
          price: product.price,
          buttonText: product.buttonText,
          aimCurrencyDisplay: product.aimCurrencyDisplay
        });
      }
    });
    
    const result = {
      headerTitle,
      headerSubtitle,
      products,
      productCount: Math.min(products.filter(p => !p.hidden).length, 2), // 默认显示2个商品
      selectedProductIndex: 0
    };
    console.log('extractMultiProductDataFromContents - products:', products);
    console.log('TemplateEditor - 最终提取结果:', result);
    return result;
    
  } catch (error) {
    console.error('TemplateEditor - 提取多商品数据时出错:', error);
    return {
      headerTitle: '',
      headerSubtitle: '',
      products: [],
      productCount: 2,
      selectedProductIndex: 0
    };
  }
};

// 从模板内容提取券+商品数据
const extractCouponProductDataFromContents = (contents) => {
  console.log('TemplateEditor - 提取券+商品数据，内容:', contents);
  
  if (!contents || contents.length === 0) {
    console.log('TemplateEditor - 没有内容数据，返回默认券+商品数据');
    return {
      headerProduct: {
        image: '',
        content: '',
        title: '',
        description: '',
        imageClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' }
      },
      coupon: {
        amount: '',
        content: '',
        title: '',
        description: '',
        buttonText: '',
        buttonClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' },
        aimCurrencyDisplay: 1
      },
      products: []
    };
  }
  
  try {
    // 手动解析券+商品数据
    const groupedByLines = {};
    contents.forEach(item => {
      if (!groupedByLines[item.pageLines]) {
        groupedByLines[item.pageLines] = [];
      }
      groupedByLines[item.pageLines].push(item);
    });

    // 初始化数据结构
    const result = {
      headerProduct: {
        image: '',
        content: '',
        title: '',
        description: '',
        imageClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' }
      },
      coupon: {
        amount: '',
        content: '',
        title: '',
        description: '',
        buttonText: '',
        buttonClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' },
        aimCurrencyDisplay: 1
      },
      products: []
    };

    // 处理第一行：商品头部信息
    const line1Contents = groupedByLines[1] || [];
    line1Contents.forEach(item => {
      if (item.type === 'image' && item.positionNumber === 1) {
        result.headerProduct.image = item.src;
        result.headerProduct.content = item.content;
        if (item.actionJson) {
          result.headerProduct.imageClickEvent = { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' };
        }
      } else if (item.type === 'text') {
        if (item.positionNumber === 2) {
          result.headerProduct.title = item.content;
        } else if (item.positionNumber === 3) {
          result.headerProduct.description = item.content;
        }
      }
    });

    // 处理第二行：券信息
    const line2Contents = groupedByLines[2] || [];
    line2Contents.forEach(item => {
      if (item.type === 'text') {
        if (item.positionNumber === 4) {
          result.coupon.amount = item.content;
          result.coupon.aimCurrencyDisplay = item.aimCurrencyDisplay !== undefined ? item.aimCurrencyDisplay : 1;
        } else if (item.positionNumber === 5) {
          result.coupon.title = item.content;
        } else if (item.positionNumber === 6) {
          result.coupon.description = item.content;
        }
      } else if (item.type === 'button' && item.positionNumber === 7) {
        result.coupon.buttonText = item.content;
        if (item.actionJson) {
          result.coupon.buttonClickEvent = { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' };
        }
      }
    });

    // 处理第三行及以后：商品列表
    const productLines = [3, 4, 5];
    productLines.forEach((line, index) => {
      const lineContents = groupedByLines[line] || [];
      if (lineContents.length === 0) return;

      const product = {
        id: `product-${index + 1}`,
        image: '',
        content: '',
        title: '',
        tag: '',
        price: '',
        buttonText: '',
        aimCurrencyDisplay: 1,
        imageClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' },
        buttonClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' },
        hidden: false
      };

      lineContents.forEach(item => {
        if (item.type === 'image') {
          if ((line === 3 && item.positionNumber === 8) || 
              (line === 4 && item.positionNumber === 13) || 
              (line === 5 && item.positionNumber === 18)) {
            product.image = item.src;
            product.content = item.content;
            if (item.actionJson) {
              product.imageClickEvent = { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' };
            }
          }
        } else if (item.type === 'text') {
          if ((line === 3 && item.positionNumber === 9) || 
              (line === 4 && item.positionNumber === 14) || 
              (line === 5 && item.positionNumber === 19)) {
            product.title = item.content;
          } else if ((line === 3 && item.positionNumber === 10) || 
                   (line === 4 && item.positionNumber === 15) || 
                   (line === 5 && item.positionNumber === 20)) {
            product.tag = item.content;
          } else if ((line === 3 && item.positionNumber === 11) || 
                   (line === 4 && item.positionNumber === 16) || 
                   (line === 5 && item.positionNumber === 21)) {
            product.price = item.content;
            product.aimCurrencyDisplay = item.aimCurrencyDisplay !== undefined ? item.aimCurrencyDisplay : 1;
          }
        } else if (item.type === 'button') {
          if ((line === 3 && item.positionNumber === 12) || 
              (line === 4 && item.positionNumber === 17) || 
              (line === 5 && item.positionNumber === 22)) {
            product.buttonText = item.content;
            if (item.actionJson) {
              product.buttonClickEvent = { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' };
            }
          }
        }
      });

      // 只有当商品有内容时才添加
      if (product.image || product.title || product.price || product.buttonText) {
        result.products.push(product);
      }
    });

    console.log('TemplateEditor - 券+商品数据提取成功:', result);
    return result;
  } catch (error) {
    console.error('TemplateEditor - 提取券+商品数据时出错:', error);
    return {
      headerProduct: {
        image: '',
        title: '',
        description: '',
        imageClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' }
      },
      coupon: {
        amount: '',
        title: '',
        description: '',
        buttonText: '',
        buttonClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' },
        aimCurrencyDisplay: 1
      },
      products: []
    };
  }
};

// 初始化券+商品设置状态
const initializeCouponProductSettingsState = (data) => {
  console.log('TemplateEditor - 初始化券+商品设置状态:', data);
  
  if (!data) {
    console.log('TemplateEditor - 没有数据，重置券+商品设置状态为null');
    couponProductSettingsState.value = null;
    return;
  }
  
  // 确保商品列表至少有3个商品
  const products = data.products || [];
  
  // 先计算实际有内容的商品数量
  const productsWithContent = products.filter(product => 
    product.title || product.image || product.price || product.tag
  );
  const actualProductCount = Math.min(Math.max(productsWithContent.length, 2), 3); // 最少2个，最多3个
  
  while (products.length < 3) {
    products.push({
      id: `product-${products.length + 1}`,
      image: '',
      title: '',
      tag: '',
      price: '',
      buttonText: '',
      imageClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' },
      buttonClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' },
      actionJson: { target: '' },
      aimCurrencyDisplay: 1,
      hidden: true
    });
  }
  
  // 根据计算的商品数量设置前面商品的可见性
  products.forEach((product, index) => {
    product.hidden = index >= actualProductCount;
  });
  
  couponProductSettingsState.value = {
    headerProduct: data.headerProduct || {
      image: '',
      title: '',
      description: '',
      imageClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' }
    },
    coupon: data.coupon || {
      amount: '',
      title: '',
      description: '',
      buttonText: '',
      buttonClickEvent: { actionType: 'OPEN_BROWSER', actionUrl: '', actionPath: '' },
      aimCurrencyDisplay: 1
    },
    products: products,
    selectedProductIndex: data.selectedProductIndex || 0,
    productCount: actualProductCount, // 使用计算出的实际商品数量
    aimCurrencyDisplay: data.aimCurrencyDisplay !== undefined ? data.aimCurrencyDisplay : 1
  };
  
  console.log('TemplateEditor - 券+商品设置状态初始化完成:', couponProductSettingsState.value);
};

// 处理券+商品设置变更
const handleCouponProductSettingsChange = (updatedContent) => {
  console.log('TemplateEditor - 接收到券+商品设置变化:', updatedContent);
  
  if (!updatedContent || !updatedContent.currentData) {
    console.log('TemplateEditor - 券+商品设置变化：内容无效');
    return;
  }
  
  // 更新券+商品设置状态
  couponProductSettingsState.value = { ...updatedContent.currentData };
  
  // 标记有变化
  hasChanges.value = true;
  hasEditedContent.value = true;
  
  // 更新实际的模板内容
  const processedContents = processCouponProductTemplateData();
  if (processedContents && processedContents.length > 0) {
    // 保持原有的contentId
    processedContents.forEach((content, index) => {
      if (selectedTemplateContents.value[index]) {
        content.contentId = selectedTemplateContents.value[index].contentId;
      }
    });
    
    // 更新内容
    selectedTemplateContents.value = processedContents;
  }
  
  console.log('TemplateEditor - 券+商品设置已更新，新状态:', couponProductSettingsState.value);
};

// 处理券+商品模板数据，用于提交
const processCouponProductTemplateData = () => {
  console.log('TemplateEditor - 开始处理券+商品模板数据');
  
  const settingsData = couponProductSettingsState.value;
  console.log('TemplateEditor - 券+商品设置数据:', settingsData);
  
  if (!settingsData) {
    console.error('TemplateEditor - 券+商品设置数据为空');
    return [];
  }
  
  let processedContents = [];
  
  // 处理头部商品图片
  if (settingsData.headerProduct) {
    const headerProduct = settingsData.headerProduct;
    const headerImageContent = {
      type: 'image',
      src: headerProduct.image || '',
      positionNumber: 1
    };
    
    // 处理头部商品图片的点击事件
    if (headerProduct.imageClickEvent && headerProduct.imageClickEvent.actionType) {
      const clickEvent = headerProduct.imageClickEvent;
      headerImageContent.actionType = clickEvent.actionType;

      // 优先使用已经生成的 actionJson，如果没有则重新生成
      if (headerProduct.actionJson) {
        headerImageContent.actionJson = headerProduct.actionJson;
      } else {
        headerImageContent.actionJson = ActionJsonGenerator.generate(
          clickEvent.actionType,
          clickEvent.actionUrl || '',
          clickEvent.actionPath || '',
          clickEvent
        );
      }
    }
    
    processedContents.push(headerImageContent);
    
    // 头部标题
    processedContents.push({
      type: 'text',
      content: headerProduct.title || '',
      positionNumber: 2,
      isTextTitle: 1
    });
    
    // 头部描述
    processedContents.push({
      type: 'text',
      content: headerProduct.description || '',
      positionNumber: 3,
      isTextTitle: 0
    });
  }
  
  // 处理券信息
  if (settingsData.coupon) {
    const coupon = settingsData.coupon;
    
    // 券金额 - 始终添加，即使为空
    processedContents.push({
      type: 'text',
      content: coupon.amount || '',
      positionNumber: 4,
      isTextTitle: 1,
      aimCurrencyDisplay: coupon.aimCurrencyDisplay !== undefined ? coupon.aimCurrencyDisplay : 1
    });
    
    // 券标题
    processedContents.push({
      type: 'text',
      content: coupon.title || '',
      positionNumber: 5,
      isTextTitle: 1
    });
    
    // 券描述
    processedContents.push({
      type: 'text',
      content: coupon.description || '',
      positionNumber: 6,
      isTextTitle: 0
    });
    
    // 券按钮
    const buttonContent = {
      type: 'button',
      content: coupon.buttonText || '',
      positionNumber: 7,
      isTextTitle: 0
    };
    
    // 处理券按钮的点击事件
    if (coupon.buttonClickEvent && coupon.buttonClickEvent.actionType) {
      const clickEvent = coupon.buttonClickEvent;
      buttonContent.actionType = clickEvent.actionType;

      // 优先使用已经生成的 actionJson，如果没有则重新生成
      if (coupon.actionJson) {
        buttonContent.actionJson = coupon.actionJson;
      } else {
        buttonContent.actionJson = ActionJsonGenerator.generate(
          clickEvent.actionType,
          clickEvent.actionUrl || '',
          clickEvent.actionPath || '',
          clickEvent
        );
      }
    }
    
    processedContents.push(buttonContent);
  }
  
  // 处理商品列表
  if (settingsData.products && Array.isArray(settingsData.products)) {
    settingsData.products.forEach((product, index) => {
      if (product.hidden) return; // 跳过隐藏的商品
      
      const basePositionNumber = 8 + (index * 5); // 每个商品占用5个位置
      
      // 商品图片
      const productImageContent = {
        type: 'image',
        src: product.image || '',
        positionNumber: basePositionNumber
      };
      
      processedContents.push(productImageContent);
      
      // 商品标题
      processedContents.push({
        type: 'text',
        content: product.title || '',
        positionNumber: basePositionNumber + 1,
        isTextTitle: 1
      });
      
      // 商品标签
      processedContents.push({
        type: 'text',
        content: product.tag || '',
        positionNumber: basePositionNumber + 2,
        isTextTitle: 0
      });
      
      // 商品价格
      processedContents.push({
        type: 'text',
        content: product.price || '',
        positionNumber: basePositionNumber + 3,
        isTextTitle: 0,
        aimCurrencyDisplay: product.aimCurrencyDisplay !== undefined ? product.aimCurrencyDisplay : 1
      });
      
      // 商品按钮
      const productButtonContent = {
        type: 'button',
        content: product.buttonText || '',
        positionNumber: basePositionNumber + 4,
        isTextTitle: 0
      };

      // 商品按钮点击事件
      if (product.buttonClickEvent && product.buttonClickEvent.actionType) {
        const clickEvent = product.buttonClickEvent;
        productButtonContent.actionType = clickEvent.actionType;

        // 优先使用已经生成的 actionJson，如果没有则重新生成
        if (product.actionJson) {
          productButtonContent.actionJson = product.actionJson;
        } else {
          // 如果没有actionJson，使用完整的clickEvent数据重新生成
          productButtonContent.actionJson = ActionJsonGenerator.generate(
            clickEvent.actionType,
            clickEvent.actionUrl || '',
            clickEvent.actionPath || '',
            clickEvent
          );
        }
      }
      
      processedContents.push(productButtonContent);
    });
  }
  console.log('processCouponProductTemplateData - 商品数据:', settingsData.products);
  console.log('TemplateEditor - 券+商品模板数据处理完成，内容数量:', processedContents.length);
  return processedContents;
};

</script>

<style scoped lang="scss">
.template-editor-container {
  width: 100%;
  height: 100%;
}

.dialog-content {
  height: calc(100vh - 114px);
  display: flex;
  background: #f5f7fa;
  position: relative;
}

.middle-section {
  position: absolute;
  display: flex;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  top: 0;
  bottom: 0;
  left: -50px;
}

.empty-template-message {
  width: 342px;
  height: 460px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  font-size: 16px;
  text-align: center;
}

:deep(.dialog-box) {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 0;
  
  .el-dialog__header {
    margin: 0;
    padding: 0;
  }
  
  .el-dialog__body {
    padding: 0;
    flex: 1;
    overflow: hidden;
  }
}
</style>


